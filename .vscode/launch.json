{
  "version": "0.2.0",
  "configurations": [
    {
      "type": "node",
      "request": "attach",
      "name": "Node: Nodemon",
      "processId": "${command:PickProcess}",
      "restart": true,
      "protocol": "inspector",
    },
    {
      "type": "node",
      "request": "launch",
      "name": "Mocha Tests",
      "program": "${workspaceFolder}/node_modules/mocha/bin/mocha",
      "args": [
        "--require", "ts-node/register",
        "--timeout", "10000",
        "--colors",
        "--recursive",
        "${workspaceFolder}/test/**/*.ts"
      ],
      "internalConsoleOptions": "neverOpen",
      "runtimeExecutable": null,
      "stopOnEntry": false,
      "protocol": "inspector",
      "sourceMaps": true,
      "console": "integratedTerminal"
    },
    {
      "type": "node",
      "request": "launch",
      "name": "Launch Program",
      "program": "${workspaceFolder}/src/main.ts",
      "preLaunchTask": "gulp: watch",
      "outFiles": ["${workspaceFolder}/build/**/*.js"],
      "protocol": "inspector",
      "restart": true,
      "cwd": "${workspaceFolder}",
      "args": []
    },
    {
      "type": "node",
      "request": "launch",
      "name": "Debug",
      "program": "${workspaceFolder}/src/main.ts",
      "preLaunchTask": "tsc: watch - tsconfig.json",
      "outFiles": ["${workspaceFolder}/build/**/*.js"],
      "restart": true,
      "cwd": "${workspaceFolder}",
      "args": ["--colors"],
      "internalConsoleOptions": "neverOpen",
      "runtimeExecutable": null,
      "stopOnEntry": false,
      "protocol": "inspector",
      "sourceMaps": true,
      "console": "integratedTerminal",
    }
  ]
}
