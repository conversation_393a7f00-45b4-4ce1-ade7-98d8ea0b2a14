"use strict";

const gulp = require("gulp");
const del = require("del");

const paths = {
  src: "src",
  build: "./build/",
  dist: "./dist/",
  unitTestFiles: ["test/unit/**/*.spec.ts"]
};

// Files that will be packaged for deployment
const distFiles = [
  "package.json",
  `${paths.build}/**/*`,
  `node_modules/**/*`,
  "ormconfig.js"
];

const jsonFiles = [
  `${paths.src}/config/*.json`,
  `${paths.src}/mockdata/*.json` // TODO: this should be like environment specific
];

gulp.task("clean", () => {
  return del([paths.build, paths.dist]);
});

gulp.task("copyJSON", () => {
  return gulp.src(jsonFiles, { base: "." }).pipe(gulp.dest("build"));
});

gulp.task("distPackage", () => {
  return gulp.src(distFiles, { base: "." }).pipe(gulp.dest("dist"));
});

gulp.task("watch", () => {
  gulp.watch([`${paths.src}/**/*.ts`], { ignoreInitial: false }, gulp.series("build"));
  gulp.watch([`${paths.src}/**/*.json`], { ignoreInitial: false }, gulp.series("copyJSON"));
});
// TODO: clean up these process as we now use tsc to build the code
gulp.task("build", gulp.series("copyJSON"));

gulp.task("dist", gulp.series("build", "distPackage"));

gulp.task("default", gulp.series("build"));
