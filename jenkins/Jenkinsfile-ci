@Library('jenkins-shared-lib-v2') _

import groovy.json.JsonOutput


def blue= "#42b3f4"
def good="#3dd62f"
def danger="#f45641"
def warning="#ffd344"


config = {}
jenkinsUtils = null

/**
 * Checks if the current commit is from jenkins
 * @return true when the current commit is from jenkins
 */
Boolean isJenkinsCommit() {
  def commitEmail = sh(script: "git log -1 --pretty=format:'%ae'", returnStdout: true)?.trim()
  return (commitEmail == "${env.GIT_COMMITTER_EMAIL}")
}

/**
 * Check if the current branch is master
 * @return true when the current branch is master
 */
Boolean isMaster() {
  def branchFullName = "${env.GIT_BRANCH}"
  def branchList = branchFullName.tokenize('/')
  def branchName = branchList.get(branchList.size()-1)
  return branchName == 'master'
}

/**
 * Authenticate session for pushing into ECR
 */
void authECR() {
  println "Authenticate to push docker image"
  ansiColor('xterm') {
    sh '''
      #!/bin/bash
      # configure system-wide environment variables and aliases needed for nvm and npm
      source /etc/profile
      export AWS_DEFAULT_REGION=us-east-1
      # Login to private container repository
      eval $(aws ecr get-login)
    '''
  }
}

pipeline {
 agent { label 'aws-ec2' }
  stages {
    stage("Repository Information") {
      steps {
        println "Repository Information"
        script {
          jenkinsUtils = load "jenkins/JenkinsUtils.groovy"
          env.RELEASE_ARTIFACTS = (!isJenkinsCommit() && isMaster() && true)
          def props = readJSON file: 'package.json'
          config.versionSnapshot = props.version
          def (versionNumber, snapshot) = config.versionSnapshot.tokenize('-')
          config.version = versionNumber
          env.VERSION_NUMBER = versionNumber
          config.repoName = jenkinsUtils.getRepoName()
        }
      }
    }
    stage("Build and Test") {
      agent {
               docker {
                         image '277983268692.dkr.ecr.us-east-1.amazonaws.com/ubuntu-22'
                         args '--volume=/var/run/docker.sock:/var/run/docker.sock -u jenkins:docker'
                      }
                }
      steps {
        println "Build"
        sshagent (credentials: ['jenkins-ssh-key'], ignoreMissing: false) {
          ansiColor('xterm') {
            sh '''
              #!/bin/bash
              # configure system-wide environment variables and aliases needed for nvm and npm
              mkdir -p ~/.ssh
              ssh-keyscan -t rsa github.com >> ~/.ssh/known_hosts
              git config user.name "AMNext-Jenkins"
              source /etc/profile
              git submodule update --init --recursive
              nvm install 22
              nvm use 22 >/dev/null
              node --version
              npm install -g yarn
              #Install nyc for coverage
              npm install -g nyc
              yarn install
              yarn dist
              # Run tests directly with Mocha
              yarn test
              # Generate coverage report using nyc
              nyc --reporter=lcov yarn test
            '''
          }
          stash includes: 'dist/**/*, coverage/**', name: 'build'
        }
      }
    }

      //added stage for coverage
           stage("publish Coverage Report")
              {
                 steps {
                  //Publish coverage report using the HTML plugin in Jenkins
                  publishHTML(target: [
                    allowMissing: true, alwaysLinkToLastBuild: false, keepAll: true,
                     reportDir: 'build/',
                    reportFiles : 'index.html',
                    reportTitles: 'Consumer Module Code Coverage',
                    reportName  : 'Consumer Module Code Coverage'
                  ])
                 }
              }

      
    stage("Checkmarx Scan") {
    agent {
                 docker {
                           image '277983268692.dkr.ecr.us-east-1.amazonaws.com/ubuntu-22'
                           args '--volume=/var/run/docker.sock:/var/run/docker.sock -u jenkins:docker'
                        }
                  }
      when { environment name: 'RELEASE_ARTIFACTS', value: 'true' }
        steps {
          step([$class: 'CxScanBuilder', comment: '', credentialsId: '', excludeFolders: '', excludeOpenSourceFolders: '',
            exclusionsSetting: 'job', failBuildOnNewResults: "${!isMaster()}",
            filterPattern: '''!**/_cvs/**/*, !**/.svn/**/*,   !**/.hg/**/*,   !**/.git/**/*,  !**/.bzr/**/*, !**/bin/**/*, !**/node_modules/**/*,
            !**/build/**/*, !**/target/**/*, !**/.gradle/**/*, !**/obj/**/*,  !**/backup/**/*, !**/.idea/**/*, !**/*.DS_Store, !**/*.ipr, !**/*.iws,
            !**/*.bak,     !**/*.tmp,       !**/*.aac,      !**/*.aif,      !**/*.iff,     !**/*.m3u, !**/*.mid, !**/*.mp3,
            !**/*.mpa,     !**/*.ra,        !**/*.wav,      !**/*.wma,      !**/*.3g2,     !**/*.3gp, !**/*.asf, !**/*.asx,
            !**/*.avi,     !**/*.flv,       !**/*.mov,      !**/*.mp4,      !**/*.mpg,     !**/*.rm,  !**/*.swf, !**/*.vob,
            !**/*.wmv,     !**/*.bmp,       !**/*.gif,      !**/*.jpg,      !**/*.png,     !**/*.psd, !**/*.tif, !**/*.swf,
            !**/*.jar,     !**/*.zip,       !**/*.rar,      !**/*.exe,      !**/*.dll,     !**/*.pdb, !**/*.7z,  !**/*.gz,
            !**/*.tar.gz,  !**/*.tar,       !**/*.gz,       !**/*.ahtm,     !**/*.ahtml,   !**/*.fhtml, !**/*.hdm,
            !**/*.hdml,    !**/*.hsql,      !**/*.ht,       !**/*.hta,      !**/*.htc,     !**/*.htd, !**/*.war, !**/*.ear,
            !**/*.htmls,   !**/*.ihtml,     !**/*.mht,      !**/*.mhtm,     !**/*.mhtml,   !**/*.ssi, !**/*.stm,
            !**/*.stml,    !**/*.ttml,      !**/*.txn,      !**/*.xhtm,     !**/*.xhtml,   !**/*.class, !**/*.iml,
            !Checkmarx/Reports/*.*''', fullScanCycle: 50, fullScansScheduled: true, groupId: '19431a46-4d94-4130-9a78-b5e2833244c6',
            includeOpenSourceFolders: '', osaArchiveIncludePatterns: '*.zip, *.war, *.ear, *.tgz',
            osaInstallBeforeScan: false, password: '{AQAAABAAAAAQZhCBOvS+ym3pI038ChU4e73PzZVrLLHSeP9Ej1IH8JU=}',
            preset: '36', projectName: "${config.repoName}", sastEnabled: true,
            serverUrl: 'https://checkmarx.loyalty.com', sourceEncoding: '1', username: ''])
      }
    }

    // stage('SonarQube Scan') {
    //   agent {
    //       docker {
    //           image '277983268692.dkr.ecr.us-east-1.amazonaws.com/ubuntu-22-slim'
    //       }
    //   }
    //   steps {
    //       script {
    //           unstash 'build'
    //           // run sonarqube scan
    //           sonarqube.checkQualityGate()
    //       }
    //   }
    //   post {
    //       cleanup {
    //           deleteDir()
    //           dir("${workspace}@tmp") {
    //               deleteDir()
    //           }
    //           dir("${workspace}@script") {
    //               deleteDir()
    //           }
    //       } 
    //   }
    // }
    stage("Release") {
     agent {
                 docker {
                           image '277983268692.dkr.ecr.us-east-1.amazonaws.com/ubuntu-22'
                           args '--volume=/var/run/docker.sock:/var/run/docker.sock -u jenkins:docker'
                        }
                  }
      when { environment name: 'RELEASE_ARTIFACTS', value: 'true' }
    	steps {
        println "Release"
    		git (
          url: "**************:LoyaltyOne/post-security-manager.git",
          branch: "master",
          credentialsId: "jenkins-ssh-key"
        )
        sshagent (credentials: ['jenkins-ssh-key'], ignoreMissing: false) {
          ansiColor('xterm') {
            sh '''
              #!/bin/bash
              # configure system-wide environment variables and aliases needed for nvm and npm
              source /etc/profile
              git branch --set-upstream-to origin/master
              nvm install 22
              nvm use 22 >/dev/null
              node --version
              npm install -g releaseme
              releaseme
            '''
          }
        }
      }
    }
    stage("Create & Publish Artifact") {
    agent {
                     docker {
                               image '277983268692.dkr.ecr.us-east-1.amazonaws.com/ubuntu-22'
                               args '--volume=/var/run/docker.sock:/var/run/docker.sock -u jenkins:docker'
                            }
                      }
      when { environment name: 'RELEASE_ARTIFACTS', value: 'true' }
    	steps {
        ansiColor('xterm') {
          sh '''
            #!/bin/bash
            # configure system-wide environment variables and aliases needed for nvm and npm
            source /etc/profile
        		export AWS_DEFAULT_REGION=us-east-1
            # Login to private container repository
            eval $(aws ecr get-login --no-include-email)
            nvm install 22
            nvm use 22
            node --version
            npm install -g yarn gulp@^4
            yarn install
            VERSION=${VERSION_NUMBER} yarn docker-publish
          '''
        }
      }
      post {
        success {
          echo 'success! Lets start up the deployment job.'
          build job: 'Deployment/post-security-manager', parameters: [[$class: 'StringParameterValue', name: 'BUILD_VERSION', value: "${config.version}"]], wait: false
        }
        failure {
          echo "failure occurred."
        }
        aborted {
          echo "job aborted."
        }
      }
    }
  }
  post {
        always {
            node('aws-ec2') {
            script {
                gitUtils.reportPRStatus()
                }
            }
        }
    }
}
