#!/usr/bin/env bash

MYSQL_CONTAINER_NAME="mysql-post-security"
MYSQL_HOST_PORT="3308"
MYSQL_USER="root"
MYSQL_PASSWORD="root" 
APP_PORT="8082"       
MAX_WAIT_SECONDS=120  # Maximum time to wait for MySQL (e.g., 2 minutes)
WAIT_INTERVAL=5       # Seconds between checks

# --- Build the application first ---
echo "Ensuring dependencies are installed..."
if ! yarn install; then
 echo "ERROR: yarn install failed for."
 exit 1
fi

echo "Building the application..."
if ! npm run build; then
 echo "ERROR: npm run build failed."
 exit 1
fi
# --- Build complete ---


echo "Checking if MySQL container '$MYSQL_CONTAINER_NAME' is running..."

# Check if the host port is already open (quick check)
nc -w 2 -z localhost $MYSQL_HOST_PORT
portIsOpen=$?

if [ $portIsOpen -ne 0 ]; then
  echo "MySQL port $MYSQL_HOST_PORT not open. Starting Docker Compose..."
  # Check if container exists but is stopped
  if [ "$(docker ps -q -f name=$MYSQL_CONTAINER_NAME)" ]; then
      echo "Container '$MYSQL_CONTAINER_NAME' exists but is stopped. Starting it..."
      docker start $MYSQL_CONTAINER_NAME
  elif [ "$(docker ps -aq -f status=exited -f name=$MYSQL_CONTAINER_NAME)" ]; then
      echo "Container '$MYSQL_CONTAINER_NAME' exists but is exited. Starting it..."
      docker start $MYSQL_CONTAINER_NAME
  else
      echo "Container '$MYSQL_CONTAINER_NAME' not found or not stopped. Running docker compose up..."

      docker compose up -d --remove-orphans
  fi


  echo "Waiting for MySQL port $MYSQL_HOST_PORT to open..."
  waited_seconds=0
  while ! nc -w 2 -z localhost $MYSQL_HOST_PORT; do
    if [ $waited_seconds -ge $MAX_WAIT_SECONDS ]; then
      echo "ERROR: MySQL port $MYSQL_HOST_PORT did not open after $MAX_WAIT_SECONDS seconds."
      docker compose logs $MYSQL_CONTAINER_NAME # Show logs for debugging
      exit 1
    fi
    echo "Port $MYSQL_HOST_PORT still not open, waiting $WAIT_INTERVAL seconds..."
    sleep $WAIT_INTERVAL
    waited_seconds=$((waited_seconds + WAIT_INTERVAL))
  done
  echo "Port $MYSQL_HOST_PORT is open."

else
  # If port is open, ensure container is actually running
  if ! docker ps -q -f name=$MYSQL_CONTAINER_NAME > /dev/null; then
      echo "Port $MYSQL_HOST_PORT is open, but container '$MYSQL_CONTAINER_NAME' is not running. Attempting to start..."
      docker start $MYSQL_CONTAINER_NAME
      sleep 5 # Give it a moment to start
      if ! docker ps -q -f name=$MYSQL_CONTAINER_NAME > /dev/null; then
          echo "ERROR: Failed to start existing container '$MYSQL_CONTAINER_NAME'."
          exit 1
      fi
  else
     echo "MySQL container '$MYSQL_CONTAINER_NAME' is already running."
  fi
fi

# --- Now, wait for MySQL server inside the container to be ready ---
echo "Waiting for MySQL server in container '$MYSQL_CONTAINER_NAME' to accept connections..."
waited_seconds=0
while ! docker exec $MYSQL_CONTAINER_NAME mysqladmin ping -h 127.0.0.1 -u $MYSQL_USER -p$MYSQL_PASSWORD --silent 2>/dev/null; do
  # Check if the container is still running, maybe it crashed?
  if ! docker ps -q -f name=$MYSQL_CONTAINER_NAME > /dev/null; then
       echo "ERROR: MySQL container '$MYSQL_CONTAINER_NAME' stopped unexpectedly while waiting for ping."
       docker compose logs $MYSQL_CONTAINER_NAME
       exit 1
  fi

  if [ $waited_seconds -ge $MAX_WAIT_SECONDS ]; then
    echo "ERROR: MySQL server in container '$MYSQL_CONTAINER_NAME' did not respond after $MAX_WAIT_SECONDS seconds."
    docker compose logs $MYSQL_CONTAINER_NAME # Show logs for debugging
    exit 1
  fi
  echo "MySQL server not ready yet, waiting $WAIT_INTERVAL seconds..."
  sleep $WAIT_INTERVAL
  waited_seconds=$((waited_seconds + WAIT_INTERVAL))
done

echo "MySQL server is ready!"
# --- SQL server Ready ---


echo "Running TypeORM schema sync and migrations..."
if ! npm run db:schema-sync; then
  echo "ERROR: npm run db:schema-sync failed."
  exit 1
fi
if ! npm run db:run-migration; then
  echo "ERROR: npm run db:run-migration failed."
  exit 1
fi

echo "TypeORM tasks complete."

# Check if the application port is in use and kill if necessary
echo "Checking if port $APP_PORT is already in use..."
nc -w 2 -z localhost $APP_PORT
appPortInUse=$?

if [ $appPortInUse -eq 0 ]; then
  echo "Port $APP_PORT is in use. Killing existing process..."
  # Use pkill for potentially cleaner process finding/killing
  if ! kill -9 $(lsof -t -i:$APP_PORT); then
    echo "WARN: Failed to kill process on port $APP_PORT. It might have already stopped."
  else
    sleep 2 # Give OS a moment to release the port
  fi
fi

echo "Starting post-security-manager on port $APP_PORT with hot reloading enabled..."
echo "Any changes to the source code will automatically restart the server."
echo "Press Ctrl+C to stop the server."

# Use yarn dev instead of npm run start to enable hot reloading
exec yarn dev
