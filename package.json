{"name": "post-security-manager", "version": "1.1.379-SNAPSHOT", "description": "Post Security Manager Service - Handles security of offers", "main": "main.js", "scripts": {"test": "mocha --require ts-node/register test/unit/**/*.spec.ts", "start": "node build/src/main.js", "build": "tsc && yarn gulp build", "dist": "yarn gulp clean && tsc && yarn gulp dist", "watch": "yarn gulp watch", "dev": "nodemon --config nodemon.json", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "docker-build": "yarn dist && NAME=post-security-manager docker-build", "docker-publish": "yarn docker-build && NAME=post-security-manager docker-push", "db:schema-drop": "typeorm schema:drop -c master", "db:schema-sync": "typeorm schema:sync -c master", "db:run-migration": "typeorm migration:run -c master", "db:revert-migration": "typeorm migration:revert -c master", "db:rebuild-test": "yarn db:schema-drop && yarn db:schema-sync && yarn db:run-migration", "typeorm": "yarn ts-node ./node_modules/typeorm/cli.js", "apidocs:generate": "yarn apidoc -f \".*\\.ts$\" --output docs/"}, "releaseme": {"steps": ["setReleaseVersion", "commitReleaseVersion", "tagRelease", "setNextVersion", "commitNextVersion", "pushChanges"]}, "docker-registry": "277983268692.dkr.ecr.us-east-1.amazonaws.com", "repository": {"type": "git", "url": "git+https://github.com/LoyaltyOne/post-security-manager.git"}, "author": "", "license": "ISC", "dependencies": {"apidoc": "^1.2.0", "aws-sdk": "^2.1692.0", "axios": "^1.6.7", "bluebird": "^3.5.3", "bunyan": "^1.8.15", "class-validator": "^0.14.0", "convict": "^6.2.3", "lodash": "^4.17.21", "mysql2": "^3.9.7", "nock": "^11.9.1", "path": "^0.12.7", "reflect-metadata": "^0.1.13", "restify": "^11.1.0", "restify-cors-middleware2": "^2.2.1", "restify-errors": "^8.0.2", "restify-router": "^0.6.2", "typedi": "^0.8.0", "typeorm": "^0.2.45"}, "devDependencies": {"@types/bluebird": "^3.5.25", "@types/hapi": "^17.8.2", "@types/lodash": "^4.14.120", "@types/mocha": "^10.0.6", "@types/node": "^20.14.10", "@types/restify-errors": "^4.3.3", "@types/sinon": "^17.0.4", "@typescript-eslint/eslint-plugin": "^8.34.1", "@typescript-eslint/parser": "^8.34.1", "chai": "^5.1.1", "del": "^3.0.0", "docker-build-run-push": "^3.0.0", "eslint": "^9.15.0", "gulp": "^5.0.0", "husky": "^8.0.3", "mocha": "^10.2.0", "nodemon": "^3.1.0", "rimraf": "^2.6.3", "sinon": "^21.0.0", "ts-node": "^8.0.2", "typescript": "^5.3.3", "typescript-eslint": "^8.34.1"}, "resolutions": {"thenify": "3.3.1", "mkdirp": "^1.0.4", "ini": "^1.3.6", "minimatch": "^3.1.2", "lodash": "4.17.21"}}