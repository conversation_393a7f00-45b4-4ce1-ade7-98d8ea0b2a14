import { createLogger } from "./log";
import { RestServer } from "./RestServer";
import { createDBConnection } from "./database/createDBConnection";

import { useContainer as ormUseContainer, ConnectionOptionsReader } from "typeorm";
import Container from "typedi";

export const APP_DB_CONNECTION_NAME = "user";
export const ADMIN_DB_CONNECTION_NAME = "master";

export async function StartupServer(config: any) {


  // Need to use do this, so that TypeDI works with TypeORM
  // https://github.com/typestack/typedi#usage-with-typescript
  ormUseContainer(Container);

  const log = createLogger(config);
  const controllers = [
    "PartnersController", "GroupsController", "UsersController",
    "OffersController"];

  await runDBMigration();
  await createDBConnection(APP_DB_CONNECTION_NAME);

  const restServer = new RestServer(config, controllers, log);

  restServer.listen(config.get("port"), (name: string, url: string) => {
    log.info("%s listening at %s", name, url);
  });
  return restServer;
}

async function runDBMigration() {
  let connection;
  try {
    connection = await createDBConnection(ADMIN_DB_CONNECTION_NAME);
    console.log("Successfully connected to the database for migration.");
  } catch (error) {
    console.error("Failed to connect to the database for migration:", error);
    process.exit(1);
  } finally {
    if (connection) {
      await connection.close();
      console.log("Migration database connection closed.");
    }
  }
}
