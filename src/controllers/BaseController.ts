import * as _ from "lodash";
import * as errors from "restify-errors";

export class BaseController {
  public bindings: any[];

  constructor() {
    // initialize instance variables
    this.bindings = [];
  }

  public initializeRestBindings(restRouter) {
    // bind rest methods to router
    _.forEach(this.bindings, (binding) => {
      // add the appropriate method handler for a specific path
      restRouter[binding.method](
        {
          name: binding.name,
          path: binding.path
        },
        (req, res, next) => {
          // handler MUST return a promise!
          //   we bind the handler to this scope and pass it the req, res, next
          //   making it as if the handler was called by the router
          const promise = binding.handler.call(this, req, res, next);

          // add error handler to promise
          promise
            .then(() => {
              next();
            })
            .catch(error => {
              console.error(error);
              if (error instanceof errors.HttpError === false) {
                req.log.error(
                  { err: error, req },
                  "Server processed raw error.  Defaulting to code='GenericError'"
                );
              }
              const errorLog = { err: error, req };
              req.log.error(
                errorLog,
                `Server responded with error from binding '${
                  binding.name
                }' at path '${binding.path}'`
              );
              return next(error);
            });
        }
      );
    });
  }
}