import * as _ from "lodash";

import {
  API_ERROR_CODES,
  REQUEST_HEADERS,
  apiErrorWrapper
} from "../constants";

import { BaseController } from "./BaseController";
import { BulkJob } from "../model/BulkJob";
import { Group } from "../entity/Group";
import { GroupPartnerMapping } from "../entity/GroupPartnerMapping";
import { GroupsService } from "../services/groups.service";
import {
  IOfferFormModel,
  IGetOffersResponse,
  ICopyText,
  IGetOffersCounts
} from "../model/Offer";
import { OffersService } from "../services/offers.service";
import { Partner } from "../model/Partner";
import { Service } from "typedi";
import { User } from "../entity/User";
import { UserPartnerDirect } from "../entity/UserPartnerDirect";
import { UsersService } from "../services/users.service";

import Errors = require("restify-errors");
import { ContentService } from "../services/content.service";
import { IGetCategoriesResponse } from "../model/Category";
import { IGetPromotionsResponse } from "../model/Promotion";
import {OfferDeliveryService} from "../services/offers.delivery.service";
import { TargetedOfferFilters } from "../model/TargetedOfferFilter";

@Service("OffersController")
export class OffersController extends BaseController {
  constructor(
    public usersService: UsersService,
    public offersService: OffersService,
    public groupsService: GroupsService,
    public contentService: ContentService,
    public offerDeliveryService: OfferDeliveryService,
  ) {
    // call base constructor
    super();
    // create bindings list
    this.bindings = [
      {
        handler: this.onBulkUploadOffers,
        method: "post",
        name: "Bulk Upload offers",
        path: "/v1/offers/bulk"
      },
      {
        handler: this.getOffersByFilters,
        method: "get",
        name: "Get Offers",
        path: "/v1/offers"
      },
      {
        handler: this.getOffersCounts,
        method: "get",
        name: "Get Offers Counts",
        path: "/v1/offers/counts"
      },
      {
        handler: this.postOffer,
        method: "post",
        name: "Post Offer",
        path: "/v1/offers"
      },
      {
        handler: this.putOffer,
        method: "put",
        name: "Put Offer",
        path: "/v1/offers/:offerId"
      },
      {
        handler: this.bulkPublishOffers,
        method: "post",
        name: "Publish offer(s)",
        path: "/v1/offers/bulk/publish"
      },
      {
        handler: this.bulkUpdateOffers,
        method: "patch",
        name: "Update offer(s)",
        path: "/v1/offers/bulk/:bulkId"
      },
      {
        handler: this.publishOffer,
        method: "post",
        name: "Publish offer",
        path: "/v1/offers/publish"
      },
      {
        handler: this.getBulkJobs,
        method: "get",
        name: "Get Bulk Jobs",
        path: "/v1/offers/jobs"
      },
      {
        handler: this.getBulkJobById,
        method: "get",
        name: "Get Bulk Jobs",
        path: "/v1/offers/jobs/:bulkId"
      },
      {
        handler: this.generateContent,
        method: "post",
        name: "Generate Copy Text Content",
        path: "/v1/offers/generate-content"
      },
      {
        // This endpoint is accessed from the UI when they are both run locally
        handler: this.getCategories,
        method: "get",
        name: "Get Categories",
        path: "/v1/categories"
      },
      {
        handler: this.deleteOffers,
        method: "post",
        name: "Delete offer(s)",
        path: "/v1/offers/delete-multiple"
      },
      {
        handler: this.disableOffer,
        method: "post",
        name: "Disable offer(s)",
        path: "/v1/offers/disable"
      },
      {
        handler: this.enableOffer,
        method: "post",
        name: "Enable offer(s)",
        path: "/v1/offers/enable"
      },
      {
        // This endpoint is accessed from the UI when they are both run locally
        handler: this.getPromotions,
        method: "get",
        name: "Get Promotions",
        path: "/v1/promotions"
      },
      {
        handler: this.getTargetedOffers,
        method: "post",
        name: "Get Targeted Offers",
        path: "/v1/offers/targeted"
      }
    ];
  }

  /**
   * @api {get} /offers Get offers for a user by filters
   * @apiName getOffersByFilters
   * @apiGroup Offers
   *
   * @apiSuccess [Offer] Array of all Offers the user has access to
   * @apiSuccessExample Success-Response:
   * HTTP/1.1 200 OK
   * [
   *     {
   *         "regions": [
   *             "AB",
   *             "BC",
   *             "MB",
   *             "NT",
   *         "ON",
   *         "SK"
   *     ],
   *     "hasCustomLegal": false,
   *     "endDate": "2019-02-22T23:59:00Z",
   *     "awardType": "cashDiscount",
   *     "availability": [
   *         "inStore"
   *     ],
   *     "canBeCombined": true,
   *     "image": {
   *         "en-US": {
   *             "path": "https://dev-post-public.s3.amazonaws.com/images/default/amCash.png"
   *         }
   *     },
   *     "mechanisms": [
   *         {
   *             "mechanismType": "noAction"
   *         }
   *     ],
   *     "partnerName": "Rexall",
   *     "offerLimitation": "noLimit",
   *     "startDate": "2019-02-22T00:00:00Z",
   *     "partnerId": "f0f6e127-e5c1-44e2-9991-d105b482e663",
   *     "offerType": "amCashDiscount",
   *     "qualifier": "cashDiscount",
   *     "displayDate": "2019-02-22T00:00:00Z",
   *     "tiers": [
   *         {
   *             "awardValue": 20,
   *             "qualifierValue": 75
   *         }
   *     ],
   *     "baseCashRedemption": 95,
   *     "displayPriority": 0
   *  }
   * ]
   *
   * @apiError
   * @apiErrorExample Error-Response:
   *     HTTP/1.1 404 Not Found Error
   *     {
   *       "code": "Not Found",
   *       "message": "User does not exist",
   *     }
   *
   * @apiError
   * @apiErrorExample Error-Response:
   *     HTTP/1.1 500 Internal Server Error
   *     {
   *       "code": "Internal",
   *       "message": "Some message",
   *     }
   */
  public async getOffersByFilters(req, res, next) {
    /**
     * 1. Call the offersHelperFunction by sending along the req
     * 2. From the response, check that the user has access to all the offers
     */

    const { user, groups, isAdmin } = await this.offersHelperFunction(req);

    const offers: IGetOffersResponse = await this.offersService.getOffersByFilters(
      req
    );
    offers.content.forEach((offer: IOfferFormModel) =>
      this.isPartnerAssignedToUser(user, groups, offer.partnerId, isAdmin)
    );

    res.send(offers);
    next();
  }

  public async getOffersCounts(req, res, next) {
    /**
     * 1. Call the offersHelperFunction by sending along the req
     * 2. From the response, return the counts of the status in an object
     */
    await this.offersHelperFunction(req);

    const offersCount: IGetOffersCounts = await this.offersService.getOffersCounts(
      req
    );

    res.send(offersCount);
    next();
  }

  public async offersHelperFunction(req) {
    /**
     * 1. Make sure a user exists
     * 2. if they pass partnerIds as a filter, check if they have permission to those
     *  a) if administrator, assume they want all partnerIds so we may not have partnerIds set
     *  b) otherwise, they may either send specific partnerId(s), or send no partner Ids. If they dont send any partner ids, we will create the partnerIds list to send for them
     * 3. Forward the query to Offer API
     * 4. return the response
     */

    const userId: string = req.header(REQUEST_HEADERS.USER_EMAIL);
    const user: User = await this.usersService.doesUserExist(userId);
    const groups: Group[] = await this.groupsService.getGroupsByUser(userId);

    const partners: Partner[] = await this.usersService.getPartnersForUser(
      userId,
      true
    );
    const isAdmin: boolean =
      groups.find((group: Group) => group.coversAllPartners) != null;

    if (!isAdmin) {
      if (!_.isEmpty(req.params) && !_.isEmpty(req.params.partnerId)) {
        // tslint:disable-next-line:no-console
        console.debug(
          "Setting Partner Ids from Params: " + req.params.partnerId
        );
        req.params.partnerId
          .split(",")
          .forEach((partnerId: string) =>
            this.isPartnerAssignedToUser(user, groups, partnerId)
          );
      } else {
        // We need to create the partnerIds list manually if they didnt select anything and are not admins
        // tslint:disable-next-line:no-console
        console.debug(
          "Setting Partner Ids from Partners: " + JSON.stringify(partners)
        );
        req.query.partnerId = partners
          .map((partner: Partner) => partner.id)
          .join(",");
      }
    }

    return {
      user,
      groups,
      isAdmin
    };
  }

  public async onBulkUploadOffers(req, res, next) {
    await this.verifyUserPermissionsOnOffers(req);

    const response = await this.offersService.uploadBulkOffers(req);
    res.send(response);
    next();
  }
  /**
   * @api {patch} /offers/bulk/{bulkId}
   * @apiName bulkUpdateOffers
   * @apiGroup Offers
   *
   * @apiSuccess Successfully updated bulk offers by using bulk id
   *
   * @apiSuccessExample Success-Response:
   * HTTP/1.1 204 No Content
   *
   * @apiError
   * @apiErrorExample Error-Response:
   *     HTTP/1.1 404 Not Found Error
   *     {
   *       "code": "Not Found",
   *       "message": "Bulk offer with that id is not found",
   *     }
   *
   * @apiError
   * @apiErrorExample Error-Response:
   *     HTTP/1.1 500 Internal Server Error
   *     {
   *       "code": "Internal",
   *       "message": "Some message",
   *     }
   */
  public async bulkUpdateOffers(req, res, next) {
    const bulkId = req.params.bulkId;
    // Ensure offer id is specified in the path params
    if (_.isEmpty(bulkId)) {
      throw new Errors.BadRequestError(
        apiErrorWrapper(API_ERROR_CODES.MISSING_BULK_ID)
      );
    }

    await this.verifyUserPermissionsOnOffers(req);

    await this.offersService.bulkUpdateOffers(req, req.params.bulkId);
    res.send(204);
    next();
  }

  /**
   * @api {post} /offers/bulk/publish
   * @apiName bulkPublishOffers
   * @apiGroup Offers
   *
   * @apiSuccess Successfully published offers by ids
   *
   * @apiSuccessExample Success-Response:
   * HTTP/1.1 204 No Content
   *
   * @apiError
   * @apiErrorExample Error-Response:
   *     HTTP/1.1 404 Not Found Error
   *     {
   *       "code": "Not Found",
   *       "message": "Offer(s) with those id(s) do not exist",
   *     }
   *
   * @apiError
   * @apiErrorExample Error-Response:
   *     HTTP/1.1 500 Internal Server Error
   *     {
   *       "code": "Internal",
   *       "message": "Some message",
   *     }
   */
  public async bulkPublishOffers(req, res, next) {
    // TODO: on the Offer API side will not be transactional, will need to update this code in the future to handle partial successes/failures
    const userId: string = req.header(REQUEST_HEADERS.USER_EMAIL);

    await this.verifyUserPermissionsOnOffersById(req, userId);

    await this.offersService.bulkPublishOffers(req);
    res.send(204);
    next();
  }

  /**
   * @api {post} /offers/publish
   * @apiName publishOffer
   * @apiGroup Offers
   *
   * @apiSuccess Successfully published offers by id
   *
   * @apiSuccessExample Success-Response:
   * HTTP/1.1 204 No Content
   *
   * @apiError
   * @apiErrorExample Error-Response:
   *     HTTP/1.1 404 Not Found Error
   *     {
   *       "code": "Not Found",
   *       "message": "Offer(s) with those id(s) do not exist",
   *     }
   *
   * @apiError
   * @apiErrorExample Error-Response:
   *     HTTP/1.1 500 Internal Server Error
   *     {
   *       "code": "Internal",
   *       "message": "Some message",
   *     }
   */
  public async publishOffer(req, res, next) {
    const userId: string = req.header(REQUEST_HEADERS.USER_EMAIL);

    await this.verifyUserPermissionsOnOffersById(req, userId);

    await this.offersService.publishOffer(req);
    res.send(204);
    next();
  }

  public async helperGetBulkJobs(req, res, next) {
    const userId: string = req.header(REQUEST_HEADERS.USER_EMAIL);

    const user: User = await this.usersService.doesUserExist(userId);
    const groups: Group[] = await this.groupsService.getGroupsByUser(userId);
    const isAdmin: boolean =
      groups.find((group: Group) => group.coversAllPartners) != null;

    let bulkJobs: BulkJob[] = await this.offersService.getBulkJobs(req);
    if (!isAdmin) {
      bulkJobs = _.chain(bulkJobs)
        .filter(job => {
          try {
            this.isPartnerAssignedToUser(user, groups, job.partnerId, isAdmin);
          } catch (err) {
            return false;
          }
          return true;
        })
        .value() as BulkJob[];
    }
    return bulkJobs;
  }
  /**
   * @api {get} /offers/jobs Gaet bulk jobs for a user
   * @apiName getBulkJobs
   * @apiGroup Offers
   *
   * @apiSuccess Array of all BulkJobs the user has access to
   *
   * @apiSuccessExample Success-Response:
   * HTTP/1.1 200 OK
   * [
   *   {
   *     "id": "ebee8ad2-99b7-42c8-86ff-f81025661112",
   *     "name": "Feb bulk Upload 8578",
   *     "createdBy": "<EMAIL>",
   *     "createdAt": "2019-03-08T15:58:57Z",
   *     "partnerId": "82bc8b9e-8b19-41d9-a9f8-2bff89bc76f9",
   *     "partnerName": "LCBO",
   *     "totalOffers": 90
   *   }
   * ]
   */
  public async getBulkJobs(req, res, next) {
    const bulkJobs: BulkJob[] = await this.helperGetBulkJobs(req, res, next);
    res.send(bulkJobs);
    next();
  }

  public async getBulkJobById(req, res, next) {
    const bulkJobs: BulkJob[] = await this.helperGetBulkJobs(req, res, next);
    if (!req.params.bulkId) {
      throw new Errors.NotFoundError(
        apiErrorWrapper(API_ERROR_CODES.BULK_JOB_NOT_FOUND, {
          error: `no bulk Id set in query param`
        })
      );
    }
    const bulkJob = _.find(bulkJobs, job => job.id === req.params.bulkId);
    if (!bulkJob) {
      throw new Errors.NotFoundError(
        apiErrorWrapper(API_ERROR_CODES.BULK_JOB_NOT_FOUND, {
          error: `no offers with ids: ${req.params.bulkId} were found`
        })
      );
    }
    res.send(bulkJob);
    next();
  }

  public async getCategories(req, res, next) {
    const categories: IGetCategoriesResponse = await this.offersService.getCategories(
      req
    );
    res.send(categories);
    next();
  }
  public async getPromotions(req, res, next) {
    const promotions: IGetPromotionsResponse = await this.offersService.getPromotions(
      req
    );
    res.send(promotions);
    next();
  }

  /**
   * @api {post} /offers/generate-content
   * @apiName generateContent
   * @apiGroup Offers
   *
   * @apiSuccess Returns The generated copy text for an associated offer
   *
   * @apiSuccessExample Success-Response:
   * HTTP/1.1 200 No Content
   *
   * {
   *  "awardLong": [
   *    {
   *      "en-US": "13 Bonus Miles",
   *      "fr-CA": "13 milles en prime"
   *    }
   *  ],
   *  "qualifierLong": [
   *    {
   *      "en-US": "Buy 24 13x the Miles*",
   *      "fr-CA": "Achetez 24 13x les milles*"
   *    }
   *  ],
   *  "awardShort": {
   *    "en-US": "13 Bonus Miles",
   *    "fr-CA": "13 milles en prime"
   *  },
   *  "qualifierShort": {
   *    "en-US": "Buy 24 13x the Miles*",
   *    "fr-CA": "Achetez 24 13x les milles*"
   *  },
   *  "legal": {
   *    "en-US": "* Offer valid from December 5, 2018 to December 25, 2018. Minimum eligible purchase must be spent in a single transaction. While supplies last. Product availability may vary by store. We reserve the right to limit quantities. Please allow up to 271 days from the offer end date for Bonus Miles to be posted to your Collector Account. AIR MILES Card must be presented at the time of the purchase. Can be combined with other offers, and AIR MILES offers. ®™ Trademarks of AM Royalties Limited Partnership used under license by LoyaltyOne, Co. and Partner Legal Name. Trademark Information",
   *    "fr-CA": "* Offre en vigueur du 5 décembre 2018 au 25 décembre 2018. L’achat minimum admissible doit être effectué en une seule transaction. Jusqu’à épuisement des stocks. La disponibilité des produits peut varier selon le magasin. Nous nous réservons le droit d’appliquer une limite aux quantités. Veuillez prévoir jusqu’à 271 jours après la fin de l’offre pour le versement des milles en prime dans votre compte d’adhérent. La carte AIR MILES doit être présentée au moment de l’achat. Peut se combiner avec d’autres offres et offres AIR MILES. md/mc Marque déposée/de commerce d'AM Royalties Limited Partnership, employée en vertu d'une licence par LoyaltyOne, Co. et par Partner Legal Name. Trademark Information"
   *  }
   * }
   * @apiError
   * @apiErrorExample Error-Response:
   *     HTTP/1.1 404 Not Found Error
   *     {
   *       "code": "Not Found",
   *       "message": "Offer(s) with those id(s) do not exist",
   *     }
   *
   * @apiError
   * @apiErrorExample Error-Response:
   *     HTTP/1.1 500 Internal Server Error
   *     {
   *       "code": "Internal",
   *       "message": "Some message",
   *     }
   */
  public async generateContent(req, res, next) {
    const requestBody = req.body;

    // Check if offer in the request body
    if (_.isEmpty(requestBody)) {
      throw new Errors.BadRequestError(
        apiErrorWrapper(API_ERROR_CODES.OFFER_REQUIRED)
      );
    }

    const copyText: ICopyText = await this.contentService.generateCopyTextContent(
      req
    );
    res.send(copyText);
    next();
  }

  public async postOffer(req, res, next) {
    const requestBody = req.body;
    const userId = req.header(REQUEST_HEADERS.USER_EMAIL);
    // Ensure request body and at least partner id is specified
    if (_.isEmpty(requestBody) || _.isEmpty(requestBody.partnerId)) {
      throw new Errors.BadRequestError(
        apiErrorWrapper(API_ERROR_CODES.UNSPECIFIED_PARTNER)
      );
    }

    const user = await this.usersService.doesUserExist(userId);
    const groups = await this.groupsService.getGroupsByUser(user.id);
    const isAdmin: boolean =
      groups.find((group: Group) => group.coversAllPartners) != null;

    // Check if partner is assigned to the current user
    this.isPartnerAssignedToUser(user, groups, requestBody.partnerId, isAdmin);

    const response = await this.offersService.postOffer(req);
    res.send(response);
    next();
  }

  public async putOffer(req, res, next) {
    const requestBody = req.body;
    const offerId = req.params.offerId;
    const userId = req.header(REQUEST_HEADERS.USER_EMAIL);
    // Ensure offer id is specified in the path params
    if (_.isEmpty(offerId)) {
      throw new Errors.BadRequestError(
        apiErrorWrapper(API_ERROR_CODES.MISSING_OFFER_ID)
      );
    }

    // Ensure request body and at least partner id is specified
    if (_.isEmpty(requestBody) || _.isEmpty(requestBody.partnerId)) {
      throw new Errors.BadRequestError(
        apiErrorWrapper(API_ERROR_CODES.UNSPECIFIED_PARTNER)
      );
    }

    const user = await this.usersService.doesUserExist(userId);
    const groups = await this.groupsService.getGroupsByUser(user.id);
    const isAdmin: boolean =
      groups.find((group: Group) => group.coversAllPartners) != null;

    // Check if partner is assigned to the current user
    this.isPartnerAssignedToUser(user, groups, requestBody.partnerId, isAdmin);

    const response = await this.offersService.putOffer(req, offerId);
    res.send(response);
    next();
  }

  /**
   * @api {post} /offers/delete-multiple
   * @apiName deleteOffers
   * @apiGroup Offers
   *
   * @apiSuccess Deletes the list of offers
   *
   * @apiSuccessExample Success-Response:
   * HTTP/1.1 200 Ok
   *
   * {
   *  "deletedOffers": [""],
   * }
   * @apiError
   * @apiErrorExample Error-Response:
   *     HTTP/1.1 400 Bad Request Error
   *     {
   *       "code": "Bad Request",
   *       "message": "Offer(s) could not be deleted",
   *     }
   *
   * @apiError
   * @apiErrorExample Error-Response:
   *     HTTP/1.1 500 Internal Server Error
   *     {
   *       "code": "Internal",
   *       "message": "Some message",
   *     }
   */
  public async deleteOffers(req, res, next) {
    const requestBody = req.body;
    const userId: string = req.header(REQUEST_HEADERS.USER_EMAIL);

    // Ensure request body is specified
    if (_.isEmpty(requestBody)) {
      throw new Errors.BadRequestError(
        apiErrorWrapper(API_ERROR_CODES.OFFER_IDS_REQUIRED)
      );
    }

    // `req.body` changed in structure for `verifyUserPermissionsOnOffersById`
    req.body = {
      id: requestBody.join(","),
    };
    await this.verifyUserPermissionsOnOffersById(
      req,
      userId
    );

    // `req.body` is pointed back to the original array that `requestBody` was holding onto.
    req.body = requestBody;
    const response = await this.offersService.deleteOffers(req);

    res.send(response);
    next();
  }

  /**
   * @api {post} /offers/disable
   * @apiName disableOffer
   * @apiGroup Offers
   *
   * @apiSuccess Disables a single offer
   *
   * @apiSuccessExample Success-Response:
   * HTTP/1.1 200 Ok
   *
   * @apiError
   * @apiErrorExample Error-Response:
   *     HTTP/1.1 400 Bad Request Error
   *     {
   *       "code": "Bad Request",
   *       "message": "Offer(s) could not be disabled",
   *     }
   *
   * @apiError
   * @apiErrorExample Error-Response:
   *     HTTP/1.1 500 Internal Server Error
   *     {
   *       "code": "Internal",
   *       "message": "Some message",
   *     }
   */
  public async disableOffer(req, res, next) {
    const requestBody = req.body;
    const userId = req.header(REQUEST_HEADERS.USER_EMAIL);

    // Ensure request body is specified
    if (_.isEmpty(requestBody)) {
      throw new Errors.BadRequestError(
        apiErrorWrapper(API_ERROR_CODES.OFFER_IDS_REQUIRED)
      );
    }

    await this.verifyUserPermissionsOnOffersById(req, userId);
    const response = await this.offersService.disableOffer(req);
    res.send(response);
    next();
  }

  /**
   * @api {get} /offers/targeted Get targeted offers for a user by collector number, date, and partner ID
   * @apiName getTargetedOffers
   * @apiGroup Offers
   */
  public async getTargetedOffers(req, res, next) {
    // Parse post params into correct format
    // const filters = new TargetedOfferFilters(req.body.collectorNumber, req.body.date, req.body.partnerId);

    const offers: any = await this.offerDeliveryService.getTargetedOffers(req)
    res.send(offers);
    next();
  }

  /**
   * @api {post} /offers/enable
   * @apiName enableOffer
   * @apiGroup Offers
   *
   * @apiSuccess Enables a single offer
   *
   * @apiSuccessExample Success-Response:
   * HTTP/1.1 200 Ok
   *
   * @apiError
   * @apiErrorExample Error-Response:
   *     HTTP/1.1 400 Bad Request Error
   *     {
   *       "code": "Bad Request",
   *       "message": "Offer(s) could not be enabled",
   *     }
   *
   * @apiError
   * @apiErrorExample Error-Response:
   *     HTTP/1.1 500 Internal Server Error
   *     {
   *       "code": "Internal",
   *       "message": "Some message",
   *     }
   */
  public async enableOffer(req, res, next) {
    const requestBody = req.body;
    const userId = req.header(REQUEST_HEADERS.USER_EMAIL);

    // Ensure request body is specified
    if (_.isEmpty(requestBody)) {
      throw new Errors.BadRequestError(
        apiErrorWrapper(API_ERROR_CODES.OFFER_IDS_REQUIRED)
      );
    }

    await this.verifyUserPermissionsOnOffersById(req, userId);
    const response = await this.offersService.enableOffer(req);
    res.send(response);
    next();
  }

  private async verifyUserPermissionsOnOffers(req: any) {
    const requestBody = req.body;

    const userId = req.header(REQUEST_HEADERS.USER_EMAIL);

    // Check if atleast one offer in the request body
    if (_.isEmpty(requestBody) || _.isEmpty(requestBody.offers)) {
      throw new Errors.BadRequestError(
        apiErrorWrapper(API_ERROR_CODES.BULK_ATLEAST_ONE_OFFER)
      );
    }

    // Check if all offers have the same partnerId
    const partnerIds: string[] = _.uniq(_.map(requestBody.offers, "partnerId"));
    if (partnerIds.length !== 1) {
      throw new Errors.BadRequestError(
        apiErrorWrapper(API_ERROR_CODES.BULK_ONLY_ONE_PARTNER)
      );
    }

    const user = await this.usersService.doesUserExist(userId);
    const groups = await this.groupsService.getGroupsByUser(user.id);
    const isAdmin: boolean =
      groups.find((group: Group) => group.coversAllPartners) != null;

    // Check if partner is assigned to the current user
    this.isPartnerAssignedToUser(user, groups, partnerIds[0], isAdmin);
  }

  /**
   * Function that verifies that the user is allowed to make a request before publishing
   * the offers
   * @param req request
   * @param userId id of the user that's making the request
   */
  private async verifyUserPermissionsOnOffersById(req: any, userId: string) {
    const requestBody = req.body;

    // Check if atleast one offer in the request body
    if (_.isEmpty(requestBody) || _.isEmpty(requestBody.id)) {
      throw new Errors.BadRequestError(
        apiErrorWrapper(API_ERROR_CODES.OFFER_IDS_REQUIRED)
      );
    }

    // so that we can make a call to fetch these offers for auth checking
    req.query.id = requestBody.id;

    const user: User = await this.usersService.doesUserExist(userId);
    const groups: Group[] = await this.groupsService.getGroupsByUser(userId);
    const isAdmin: boolean =
      groups.find((group: Group) => group.coversAllPartners) != null;

    // Grab the offer first to do our checks against it
    const getOffersResponse: IGetOffersResponse = await this.offersService.getOffersByFilters(
      req
    );

    const offers: IOfferFormModel[] = getOffersResponse.content;
    if (!offers.length) {
      throw new Errors.NotFoundError(
        apiErrorWrapper(API_ERROR_CODES.OFFER_NOT_FOUND, {
          error: `no offers with ids: ${requestBody.id} were found`
        })
      );
    } else {
      // We need to make sure that for all the offers we requested to publish, they all were retrieved from the offer API
      const requestOfferIds: string[] = requestBody.id.split(",");
      const offersWithOnlyIds: string[] = offers.map(
        (offer: IOfferFormModel) => offer.id
      );
      requestOfferIds.forEach((requestOfferId: string) => {
        if (offersWithOnlyIds.indexOf(requestOfferId) < 0) {
          throw new Errors.NotFoundError(
            apiErrorWrapper(API_ERROR_CODES.OFFER_NOT_FOUND, {
              error: `offer with id ${requestOfferId} was not found`
            })
          );
        }
      });
    }

    if (!isAdmin) {
      offers.forEach((offer: IOfferFormModel) =>
        this.isPartnerAssignedToUser(user, groups, offer.partnerId, isAdmin)
      );
    }
  }

  /**
   * Function to check if a partner is assigned to a user. Throws error if not
   * @param partnerId
   */
  private isPartnerAssignedToUser(
    user: User,
    groups: Group[],
    partnerId: string,
    isAdmin: boolean = false
  ) {
    if (isAdmin) {
      return;
    }

    // Check if partner is directly assigned to user
    const partnerDirectIds: string[] = user.partners
      ? _.map(user.partners, (partner: UserPartnerDirect) => partner.partnerId)
      : [];
    if (_.indexOf(partnerDirectIds, partnerId) > -1) {
      return;
    }

    // Check if partner is in one of the groups assigned to user
    const groupPartnerIds: string[] = groups
      ? _.uniq(
          _.flatMap(groups, (group: Group) =>
            _.map(
              group.partnerMappings,
              (partner: GroupPartnerMapping) => partner.partnerId
            )
          )
        )
      : [];
    if (_.indexOf(groupPartnerIds, partnerId) > -1) {
      return;
    }

    const error = `${API_ERROR_CODES.PARTNER_NOT_ASSIGNED_TO_USER.message}; user: ${user.id}, partner: ${partnerId}`;
    // tslint:disable-next-line:no-console
    console.error(error);
    throw new Errors.BadRequestError(
      apiErrorWrapper(API_ERROR_CODES.PARTNER_NOT_ASSIGNED_TO_USER, { error })
    );
  }
}
