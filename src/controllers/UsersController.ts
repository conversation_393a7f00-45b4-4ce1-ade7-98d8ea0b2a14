import { GroupsService } from "../services/groups.service";
import { BaseController } from "./BaseController";
import { Service } from "typedi";
import { UsersService } from "../services/users.service";
import * as _ from "lodash";
import { PartnersService } from "../services/partners.service";
import { UserPartnerDirect } from "../entity/UserPartnerDirect";
import { User } from "../entity/User";
import {
  apiErrorWrapper,
  API_ERROR_CODES,
  REQUEST_HEADERS
} from "../constants";
import * as Errors from "restify-errors";
import { Partner } from "../model/Partner";
import { Group } from "../entity/Group";
import { GroupPartnerMapping } from "../entity/GroupPartnerMapping";

@Service("UsersController")
export class UsersController extends BaseController {
  constructor(
    private groupsService: GroupsService,
    private usersService: UsersService,
    private partnersService: PartnersService
  ) {
    // call base constructor
    super();
    // create bindings list
    this.bindings = [
      {
        handler: this.addGroupToUser,
        method: "post",
        name: "Add group to user",
        path: "/users/:userId/groups"
      },
      {
        handler: this.removeGroupFromUser,
        method: "del",
        name: "Remove group from user",
        path: "/users/:userId/groups/:groupId"
      },
      {
        handler: this.addPartnerToUser,
        method: "post",
        name: "Add Partner to User",
        path: "/users/:userId/partners"
      },
      {
        handler: this.removePartnerFromUser,
        method: "del",
        name: "Remove partner from user",
        path: "/users/:userId/partners/:partnerId"
      },
      {
        handler: this.onGetGroupsForUser,
        method: "get",
        name: "Get groups for user",
        path: "/users/:userId/groups"
      },
      {
        handler: this.onGetPartnersForUser,
        method: "get",
        name: "Get partners for user",
        path: "/users/:userId/partners"
      }
    ];
  }

  /**
   * @api {post} /users/:userId/groups Add Group to User by ID
   * @apiName addGroupToUser
   * @apiGroup Users
   *
   * @apiSuccessExample Success-Response:
   *     HTTP/1.1 200 OK
   *       {
   *           "groupId": "groupId",
   *           "userId": "userId"
   *       }
   *
   *
   * @apiError
   * @apiErrorExample Error-Response:
   *     HTTP/1.1 404 Not found
   *     {
   *          "code": "GROUP_NOT_FOUND",
   *          "message": "Group not found`"
   *     }
   * @apiError
   * @apiErrorExample Error-Response:
   *     HTTP/1.1 404 Not found
   *     {
   *          "code": "USER_NOT_FOUND",
   *          "message": "User not found`"
   *     }
   */
  public async addGroupToUser(req, res, next) {
    const requestingUserId: string = req.header(REQUEST_HEADERS.USER_EMAIL);

    const userId: string = req.params.userId;
    const groupId: number = _.parseInt(req.body.groupId);
    await this.groupsService.doesGroupExist(groupId);
    await this.addUserIfDoesNotExist(userId);
    await this.usersService.addGroupToUser(userId, groupId, requestingUserId);
    const groups = await this.getGroupsForUser(userId);
    res.send(groups);
    next();
  }

  /**
   * @api {post} /users/:userId/partners Add Partner to User by ID
   * @apiName addPartnerToUser
   * @apiGroup Users
   *
   * @apiSuccessExample Success-Response:
   *     HTTP/1.1 200 OK
   *       {
   *           "partnerId": "partnerId",
   *           "userId": "userId"
   *       }
   *
   *
   * @apiError
   * @apiErrorExample Error-Response:
   *     HTTP/1.1 404 Not found
   *     {
   *          "code": "PARTNER_NOT_FOUND",
   *          "message": "Partner not found`"
   *     }
   * @apiError
   * @apiErrorExample Error-Response:
   *     HTTP/1.1 404 Not found
   *     {
   *          "code": "USER_NOT_FOUND",
   *          "message": "User not found`"
   *     }
   */
  public async addPartnerToUser(req, res, next) {
    const requestingUserId: string = req.header(REQUEST_HEADERS.USER_EMAIL);
    const userId: string = req.params.userId;
    const partnerId: string = req.body.partnerId;
    await this.partnersService.doesPartnerExist(partnerId);
    await this.addUserIfDoesNotExist(userId);
    await this.usersService.addPartnerToUser(
      userId,
      partnerId,
      requestingUserId
    );
    const partners = await this.usersService.getPartnersForUser(userId);
    res.send(partners);
    next();
  }

  /**
   * @api {delete} /users/:userId/groups/:groupId Remove group from user
   * @apiName removeGroupFromUser
   * @apiGroup Users
   *
   * @apiSuccessExample Success-Response:
   *     HTTP/1.1 200 OK
   *
   * @apiError
   * @apiErrorExample Error-Response:
   *     HTTP/1.1 404 Not found
   *     {
   *          "code": "GROUP_NOT_FOUND",
   *          "message": "Group not found`"
   *     }
   * @apiError
   * @apiErrorExample Error-Response:
   *     HTTP/1.1 404 Not found
   *     {
   *          "code": "USER_NOT_FOUND",
   *          "message": "User not found`"
   *     }
   */
  public async removeGroupFromUser(req, res, next) {
    const userId: string = req.params.userId;
    const groupId: number = _.parseInt(req.params.groupId);
    await this.usersService.doesUserExist(userId);
    await this.groupsService.doesGroupExist(groupId);
    await this.usersService.removeGroupFromUser(userId, groupId);
    const groups = await this.getGroupsForUser(userId);
    res.send(groups);
    next();
  }

  /**
   * @api {delete} /users/:userId/partners/:partnerId Remove partner from user
   * @apiName removePartnerFromUser
   * @apiGroup Users
   *
   * @apiSuccessExample Success-Response:
   *     HTTP/1.1 200 OK
   *
   * @apiError
   * @apiErrorExample Error-Response:
   *     HTTP/1.1 404 Not found
   *     {
   *          "code": "PARTNER_NOT_FOUND",
   *          "message": "Partner not found`"
   *     }
   * @apiError
   * @apiErrorExample Error-Response:
   *     HTTP/1.1 404 Not found
   *     {
   *          "code": "USER_NOT_FOUND",
   *          "message": "User not found`"
   *     }
   */
  public async removePartnerFromUser(req, res, next) {
    const userId: string = req.params.userId;
    const partnerId: string = req.params.partnerId;
    await this.usersService.doesUserExist(userId);
    await this.partnersService.doesPartnerExist(partnerId);
    await this.usersService.removePartnerFromUser(userId, partnerId);
    const partners = await this.usersService.getPartnersForUser(userId);
    res.send(partners);
    next();
  }

  /**
   * @api {get} /users/:userId/groups Get Groups for User
   * @apiName onGetGroupsForUser
   * @apiGroup Users
   *
   * @apiSuccessExample Success-Response:
   *     HTTP/1.1 200 OK
   *       [{
   *     "id": 201,
   *     "name": "Group2",
   *     "coversAllPartners": false,
   *     "updatedDate": "2019-01-29T22:17:55.449Z",
   *     "version": 1,
   *     "createdBy": null,
   *     "updatedBy": null,
   *     "users": [
   *         {
   *             "userId": "<EMAIL>",
   *             "groupId": 201,
   *             "createdDate": "2019-02-06T22:34:37.630Z"
   *         }
   *     ],
   *     "partnerMappings": [
   *         {
   *             "groupId": 201,
   *             "partnerId": "4a755252-876b-478e-9440-42961525e307"
   *         }
   *     ],
   *     "partners": [
   *         {
   *             "name": "Metro",
   *             "sponsorCodes": [
   *                 "APAP",
   *                 "APAT",
   *                 "APDO",
   *                 "APUL",
   *                 "BARN"
   *             ],
   *             "type": [
   *                 "in-store",
   *                 "cash"
   *             ],
   *             "fullLogo": [
   *                 {
   *                     "title": "@2x",
   *                     "file": {
   *                         "url": "//images.ctfassets.net/hpbflulg5svm/7HeIncCJX2qCQcSakWmSEo/a4dfc2d8e2410ad2643ad732f7bbe69e/Metro.png",
   *                         "details": {
   *                             "size": 20117,
   *                             "image": {
   *                                 "width": 512,
   *                                 "height": 512
   *                             }
   *                         },
   *                         "fileName": "Metro.png",
   *                         "contentType": "image/png"
   *                     }
   *                 }
   *             ],
   *             "baseEarnRate": "1 Mile for every $20 spent in a week (Sunday to Saturday) Extra text for the demo Extra text for the demo Extra text for the demo Extra text for the demo Extra text for the demo Extra text for the demo",
   *             "maxCashMiles": "Use cash miles here! Up to $100 (950 Cash Miles) towards your purchase in-store, per day. Extra text for the demo Extra text for the demo Extra text for the demo Extra text for the demo Extra text for the demo Extra text for the demo",
   *             "regions": [
   *                 "on"
   *             ],
   *             "priority": 10,
   *             "id": "4a755252-876b-478e-9440-42961525e307",
   *             "createdAt": "2017-05-17T13:18:34.403Z",
   *             "updatedAt": "2018-09-24T17:45:57.250Z",
   *             "revision": 8
   *         }]
   * }]
   *
   * @apiError
   * @apiErrorExample Error-Response:
   *     HTTP/1.1 404 Not found
   *     {
   *          "code": "USER_NOT_FOUND",
   *          "message": "User not found`"
   *     }
   */
  public async onGetGroupsForUser(req, res, next) {
    const userId: string = req.params.userId;
    await this.usersService.doesUserExist(userId);
    const groups = await this.getGroupsForUser(userId);
    res.send(groups);
    next();
  }

  /**
   * @api {get} /users/:userId/partners Get Partners for User by ID
   * @apiName onGetPartnersForUser
   * @apiGroup Users
   *
   * @apiSuccessExample Success-Response:
   *     HTTP/1.1 200 OK
   *      {
   *     "name": "Metro",
   *     "sponsorCodes": [
   *         "APAP",
   *         "APAT",
   *         "APDO",
   *         "APUL",
   *         "BARN"
   *     ],
   *     "type": [
   *         "in-store",
   *         "cash"
   *     ],
   *     "fullLogo": [
   *         {
   *             "title": "@2x",
   *             "file": {
   *                 "url": "//images.ctfassets.net/hpbflulg5svm/7HeIncCJX2qCQcSakWmSEo/a4dfc2d8e2410ad2643ad732f7bbe69e/Metro.png",
   *                 "details": {
   *                     "size": 20117,
   *                     "image": {
   *                         "width": 512,
   *                         "height": 512
   *                     }
   *                 },
   *                 "fileName": "Metro.png",
   *                 "contentType": "image/png"
   *             }
   *         }
   *     ],
   *     "baseEarnRate": "1 Mile for every $20 spent in a week (Sunday to Saturday) Extra text for the demo Extra text for the demo Extra text for the demo Extra text for the demo Extra text for the demo Extra text for the demo",
   *     "maxCashMiles": "Use cash miles here! Up to $100 (950 Cash Miles) towards your purchase in-store, per day. Extra text for the demo Extra text for the demo Extra text for the demo Extra text for the demo Extra text for the demo Extra text for the demo",
   *     "regions": [
   *         "on"
   *     ],
   *     "priority": 10,
   *     "id": "4a755252-876b-478e-9440-42961525e307",
   *     "createdAt": "2017-05-17T13:18:34.403Z",
   *     "updatedAt": "2018-09-24T17:45:57.250Z",
   *     "revision": 8
   * }
   *
   * @apiError
   * @apiErrorExample Error-Response:
   *     HTTP/1.1 404 Not found
   *     {
   *          "code": "USER_NOT_FOUND",
   *          "message": "User not found`"
   *     }
   */
  public async onGetPartnersForUser(req, res, next) {
    const userId: string = req.params.userId;
    const includeGroupPartners: boolean =
      req.params.includeGroupPartners || false;
    await this.usersService.doesUserExist(userId);
    const partners = await this.usersService.getPartnersForUser(
      userId,
      includeGroupPartners
    );
    res.send(partners);
    next();
  }

  /**
   * Helper function to add a user if it does not exist.
   * @param userId
   */
  private async addUserIfDoesNotExist(userId: string): Promise<User> {
    if (!userId) {
      throw new Errors.BadRequestError(
        apiErrorWrapper(API_ERROR_CODES.USER_NOT_FOUND)
      );
    }
    let user = await this.usersService.getUserById(userId);
    if (!user) {
      const newUser = new User();
      newUser.id = userId;
      user = await this.usersService.addUser(newUser);
    }
    return user;
  }

  private async getGroupsForUser(userId: string): Promise<Group[]> {
    const groups = await this.groupsService.getGroupsByUser(userId);
    return await this.groupsService.getGroupsWithPartnerDetails(groups);
  }

  private async getUserFromEmail(email: string): Promise<User> {
    const user: User = await this.usersService.doesUserExist(email);

    if (!user) {
      throw new Errors.BadRequestError(
        apiErrorWrapper(API_ERROR_CODES.USER_NOT_FOUND)
      );
    }
    return user;
  }
}
