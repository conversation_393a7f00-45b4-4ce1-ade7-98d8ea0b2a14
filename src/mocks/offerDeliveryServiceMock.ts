import * as nock from "nock";

import { Config } from "../config/config";
import Container from "typedi";

export function createOfferDeliveryServiceMock() {
  const config: Config = Container.get("Config");
  const baseURL = config.config.get("offerDeliveryEndpoint");
  generateOfferDeliveryMocks(baseURL);
}

function generateOfferDeliveryMocks(baseURL: string) {
  // `query(true)` is specified as "req.params" that are being passed blindly to query params contains
  // body of the request as well

  nock(baseURL, { allowUnmocked: true })
    .post("/api/v1/infrastructure/scripts/GetProfileProgramOffers/invoke")
    .query(true)
    .reply(200, { data: [{ id: "test-guid-id" }] })
    .persist();
}
