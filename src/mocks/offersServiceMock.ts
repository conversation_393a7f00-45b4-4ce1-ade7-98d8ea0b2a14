import * as nock from "nock";

import { Config } from "../config/config";
import Container from "typedi";

import * as _ from "lodash";

export function createOffersServiceMock() {
  const config: Config = Container.get("Config");
  const baseURL = config.config.get("offersEndpoint");
  generateOffersMocks(baseURL);
}

export function createBulkJobsMock() {
  const config: Config = Container.get("Config");
  const baseURL = config.config.get("offersEndpoint");
  generateBulkUploadMocks(baseURL);
}

function generateOffersMocks(baseURL: string) {
  // `query(true)` is specified as "req.params" that are being passed blindly to query params contains
  // body of the request as well

  nock(baseURL, { allowUnmocked: true })
    .get("/offers")
    .query(true)
    .reply(200, require("../mockdata/sample_offers.json"))
    .persist();

  nock(baseURL, { allowUnmocked: true })
    .get("/offers/counts")
    .query(true)
    .reply(200, require("../mockdata/sample_offers_counts.json"))
    .persist();

  // Mock POST offers
  nock(baseURL, { allowUnmocked: true })
    .post(
      "/offers",
      body => body.baseCashRedemption !== 666 && body.baseCashRedemption !== 777
    )
    .query(true)
    .reply(201, require("../mockdata/successful_offer_creation.json"))
    .persist();

  nock(baseURL, { allowUnmocked: true })
    .post("/offers", _.matches({ baseCashRedemption: 666 }))
    .query(true)
    .reply(500, { code: "500_ERROR", message: "Some message" })
    .persist();

  nock(baseURL, { allowUnmocked: true })
    .post("/offers", _.matches({ baseCashRedemption: 777 }))
    .query(true)
    .reply(400, { code: "400_ERROR", message: "Some message" })
    .persist();

  // Mock delete (POST) offers
  nock(baseURL, { allowUnmocked: true })
    .post("/offers/delete-multiple")
    .query(true)
    .reply(200, { deletedOffers: ["123"] })
    .persist();

  nock(baseURL, { allowUnmocked: true })
    .post("/offers/targeted")
    .query(true)
    .reply(200, [{ SysOfferName: "123" }, { SysOfferName: "456" }])
    .persist();

  // Mock disable (POST) offers
  nock(baseURL, { allowUnmocked: true })
    .post("/offers/disable")
    .query(true)
    .reply(201, require("../mockdata/successful_offer_creation.json"))
    .persist();

  // Mock enable (POST) offer
  nock(baseURL, { allowUnmocked: true })
    .post("/offers/enable")
    .query(true)
    .reply(201, require("../mockdata/successful_offer_creation.json"))
    .persist();

  // Mock PUT offers
  nock(baseURL, { allowUnmocked: true })
    .put(
      /\/offers\/[^/]*$/,
      body => body.baseCashRedemption !== 666 && body.baseCashRedemption !== 777
    )
    .query(true)
    .reply(201, require("../mockdata/successful_offer_creation.json"))
    .persist();

  nock(baseURL, { allowUnmocked: true })
    .put(/\/offers\/[^/]*$/, _.matches({ baseCashRedemption: 666 }))
    .query(true)
    .reply(500, { code: "500_ERROR", message: "Some message" })
    .persist();

  nock(baseURL, { allowUnmocked: true })
    .put(/\/offers\/[^/]*$/, _.matches({ baseCashRedemption: 777 }))
    .query(true)
    .reply(400, { code: "400_ERROR", message: "Some message" })
    .persist();
}

function generateBulkUploadMocks(baseURL: string) {
  // Default case
  nock(baseURL, { allowUnmocked: true })
    .post(
      "/offers/bulk",
      body => body.bulkName !== "500Error" && body.bulkName !== "400Error"
    )
    .reply(200, { id: "bulkId" })
    .persist();

  // 500 Errors
  nock(baseURL, { allowUnmocked: true })
    .post("/offers/bulk", _.matches({ bulkName: "500Error" }))
    .reply(500, { code: "500_ERROR", message: "Some message" })
    .persist();

  // 400 Errors
  nock(baseURL, { allowUnmocked: true })
    .post("/offers/bulk", _.matches({ bulkName: "400Error" }))
    .reply(400, { code: "400_ERROR", message: "Some message" })
    .persist();

  // 200
  nock(baseURL, { allowUnmocked: true })
    .patch(/\/offers\/bulk\/[^/]*$/)
    .reply(200)
    .persist();

  nock(baseURL, { allowUnmocked: true })
    .post("/offers/bulk/publish")
    .reply(200)
    .persist();

  nock(baseURL, { allowUnmocked: true })
    .post(
      "/offers/publish",
      body => body.id === "df93b838-f2e2-4e46-bf7e-eadd4cbd52cc"
    )
    .reply(504, { message: "Endpoint request timed out" })
    .post("/offers/publish")
    .reply(200)
    .persist();

  nock(baseURL, { allowUnmocked: true })
    .get("/offers/jobs")
    .reply(200, require("../mockdata/sample_bulk_jobs.json"))
    .persist();
}

export function createCategoriesMock() {
  const config: Config = Container.get("Config");
  const baseURL = config.config.get("offersEndpoint");
  generateCategoriesMocks(baseURL);
}

function generateCategoriesMocks(baseURL: string) {
  // Default case
  nock(baseURL, { allowUnmocked: true })
    .get("/categories")
    .query(true)
    .reply(200, require("../mockdata/categories.json"))
    .persist();
}

export function createPromotionsMock() {
  const config: Config = Container.get("Config");
  const baseURL = config.config.get("offersEndpoint");
  generatePromotionsMocks(baseURL);
}

function generatePromotionsMocks(baseURL: string) {
  // Default case
  nock(baseURL, { allowUnmocked: true })
    .get("/promotions")
    .query(true)
    .reply(200, require("../mockdata/promotions.json"))
    .persist();
}
