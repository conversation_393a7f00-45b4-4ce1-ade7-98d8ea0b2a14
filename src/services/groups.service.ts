import { Repository, DeleteResult } from "typeorm";
import { Group } from "../entity/Group";
import { Service } from "typedi";
import * as Errors from "restify-errors";
import { validate } from "class-validator";
import { ORM_ERRORS } from "../errors/orm.errors";
import { GroupPartnerMapping } from "../entity/GroupPartnerMapping";
import { getRepositoryFromContainer } from "../utils/repository.utils";
import { apiErrorWrapper, API_ERROR_CODES } from "../constants";
import { UserGroup } from "../entity/UserGroup";

import { Partner } from "../model/Partner";
import { PartnersService } from "./partners.service";
import { IApiResponse } from "../interface/IApiResponse";
import { User } from "../entity/User";

@Service("GroupsService")
export class GroupsService {
  private groupRepository: Repository<Group>;
  private groupPartnersRepository: Repository<GroupPartnerMapping>;

  constructor(private partnersService: PartnersService) {
    this.groupRepository = getRepositoryFromContainer("GroupRepository", Group);
    this.groupPartnersRepository = getRepositoryFromContainer(
      "GroupPartnerMappingRepository",
      GroupPartnerMapping
    );
  }

  public getGroupById(groupId: number): Promise<Group> {
    return this.groupRepository.findOne(groupId, {
      relations: ["partnerMappings"]
    });
  }

  public getAllGroups(): Promise<Group[]> {
    return Promise.resolve(
      this.groupRepository.find({ relations: ["partnerMappings"] })
    );
  }

  public async getGroupsByUser(userId: string): Promise<Group[]> {
    return await this.groupRepository
      .createQueryBuilder("groups")
      .innerJoinAndSelect("groups.users", "user")
      .leftJoinAndSelect("groups.partnerMappings", "partner")
      .where("user.userId = :userId", { userId })
      .getMany();
  }

  /**
   * Returns an updated list of groups with the partner details populated
   * @param groups
   */
  public async getGroupsWithPartnerDetails(groups: Group[]): Promise<Group[]> {
    let partners: Partner[] = [];
    const allPartnerIds: string[] = [];
    groups.forEach((group: Group) =>
      group.partnerMappings.forEach((partner: GroupPartnerMapping) => {
        if (allPartnerIds.indexOf(partner.partnerId) < 0) {
          allPartnerIds.push(partner.partnerId);
        }
      })
    );
    if (allPartnerIds.length) {
      // TODO: check for other error types
      partners = await this.partnersService.getPartnersByIds(allPartnerIds);
    }
    // Iterate through the existing partners for the groups, and assign them the proper partners where applicable
    groups.forEach((group: Group) => {
      group.partners = group.partnerMappings.map(
        (groupPartnerMapping: GroupPartnerMapping) => {
          return partners.find(
            (partner: Partner) => partner.id === groupPartnerMapping.partnerId
          );
        }
      );
    });
    return groups;
  }

  public async addNewGroup(
    group: Group,
    requestingUserId: string
  ): Promise<Group> {
    const validationErrors = await validate(group);
    if (validationErrors.length > 0) {
      throw new Errors.InvalidContentError(
        apiErrorWrapper(API_ERROR_CODES.VALIDATION_ERRORS, { validationErrors })
      );
    } else {
      group.createdBy = requestingUserId;
      group.updatedBy = requestingUserId;

      // TODO: need to deal with 500 Errors
      const newGroup = this.groupRepository.create(group);
      try {
        return await this.groupRepository.save(newGroup);
      } catch (error) {
        // TODO: have to catch malformed entities
        if (error.code === ORM_ERRORS.DUPLICATE_ENTRY) {
          throw new Errors.ConflictError(
            apiErrorWrapper(API_ERROR_CODES.GROUP_DUPLICATE_NAME)
          );
        }
      }
    }
  }

  public async addPartnerToGroup(
    groupId: number,
    partnerId: string,
    requestingUserId: string
  ): Promise<GroupPartnerMapping> {
    // TODO: fix unit tests
    const partner: GroupPartnerMapping = this.groupPartnersRepository.create();
    partner.groupId = groupId;
    partner.partnerId = partnerId;
    partner.createdBy = requestingUserId;
    try {
      return await this.groupPartnersRepository.save(partner);
    } catch (error) {
      // TODO: have to catch malformed entities
      if (error.code === ORM_ERRORS.DUPLICATE_ENTRY) {
        throw new Errors.ConflictError(
          apiErrorWrapper(API_ERROR_CODES.PARTNER_DUPLICATE)
        );
      }
      throw new Errors.InternalError(error);
    }
  }

  public async deletePartnerFromGroup(
    groupId: number,
    partnerId: string
  ): Promise<void> {
    const partnerMapping: GroupPartnerMapping = await this.groupPartnersRepository.findOne(
      { where: { partnerId, groupId } }
    );
    // If the partner has already been deleted, dont do anything
    if (!partnerMapping) {
      return Promise.resolve();
    }

    const deleteResult: DeleteResult = await this.groupPartnersRepository.delete(
      {
        partnerId: partnerMapping.partnerId,
        groupId: partnerMapping.groupId
      }
    );
    if (deleteResult.affected) {
      return Promise.resolve();
    } else {
      throw new Errors.InternalError(
        apiErrorWrapper(API_ERROR_CODES.INTERNAL_ERROR),
        "Failed to delete partner from group"
      );
    }
  }

  public async getUsersByGroupId(groupId: number): Promise<UserGroup[]> {
    return (await this.groupRepository.findOne(groupId, {
      relations: ["users"]
    })).users;
  }

  public async doesGroupExist(groupId: number): Promise<Group> {
    if (!groupId) {
      throw new Errors.BadRequestError(
        apiErrorWrapper(API_ERROR_CODES.GROUP_NOT_FOUND)
      );
    }
    const group: Group = await this.getGroupById(groupId);
    if (!group) {
      throw new Errors.NotFoundError(
        apiErrorWrapper(API_ERROR_CODES.GROUP_NOT_FOUND)
      );
    }
    return group;
  }
}
