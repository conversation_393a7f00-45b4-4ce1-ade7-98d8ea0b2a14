import { Service } from "typedi";
import { Config } from "../config/config";
import axios from "axios";
import Errors = require("restify-errors");
import { apiErrorWrapper, REQUEST_HEADERS } from "../constants";
import { ITargetedOfferFilters } from "../model/TargetedOfferFilter";
import { createOfferDeliveryServiceMock } from "../mocks/offerDeliveryServiceMock";

export const DELIVERY_API = {
  GET_COLLECTOR_OFFERS: '/offer-delivery-service/collector/offers',
};

@Service("OfferDeliveryService")
export class OfferDeliveryService {
  private REQUIRED_HEADERS: string[] = [REQUEST_HEADERS.USER_EMAIL];

  constructor(private config: Config) {
    if (this.config.config.get("useMockOffers")) {
      // tslint:disable-next-line: no-console
      createOfferDeliveryServiceMock();
    }
  }

  public async getTargetedOffers(req: any): Promise<any> {

    const headers:{ [s: string]: string } = {
      "Collector-Number": req.body.collectorNumber,
      "X-Timestamp": req.body.date+"T00:00:00",
      "x-origin-client": "internal:amrp:post",
      "x-correlation-id": req.body.correlationId
    }

    req.query = {
      "partnerId": req.body.partnerId,
      "massOffer": "false"
    }
    req.method = 'get';

    return await this.makeEndpointCall(
      DELIVERY_API.GET_COLLECTOR_OFFERS,
      req,
      false,
      headers
    );
  }

  /**
   * Allows us to call mock or actual endpoint
   * @param path the path on the offers endpoint to call
   */
  private async makeEndpointCall(
    path: string,
    req: any,
    skipQueryParams?: boolean,
    additionalHeaders?: { [s: string]: string }
  ) {
    try {
      // Make sure we send requests as id=1&id=2 instead of id=1,2 | only for query params that should be appended to the URL
      const values: { [key: string]: string[] } = {};
      if (req.query) {
        Object.keys(req.query).forEach(key => {
          if (req.query[key]) {
            values[key] = req.query[key].split(",");
          }
        });
      }

      const requestConfig = {
        url: `${this.config.config.get("offerDeliveryEndpoint")}${path}`,
        headers: this.prepareRequestHeaders(req.headers, additionalHeaders),
        data: req.body,
        method: req.method,
        params: skipQueryParams ? undefined : values,
        paramsSerializer: {
          indexes: null // equivalent to indices: false in request
        }
      };

      // tslint:disable-next-line:no-console
      console.debug(
        "Making a request with params " + JSON.stringify(requestConfig)
      );

      const response = await axios(requestConfig);
      // tslint:disable-next-line:no-console
      console.debug("Response: " + JSON.stringify(response.data));
      return response.data;
    } catch (e) {
      if (e.response?.status === 404) {
        return {"data":[]};
      }
      this.throwRestifyError(e);
    }
  }

  private throwRestifyError(e) {
    // tslint:disable-next-line:no-console
    console.error(e);
    const error = apiErrorWrapper(e.response?.data || e.message);
    switch (e.response?.status) {
      case 400:
        throw new Errors.BadRequestError(error);
      case 503:
        throw new Errors.ServiceUnavailableError(error);
      case 504:
        throw new Errors.GatewayTimeoutError(error);
      default:
        throw new Errors.InternalServerError(error);
    }
  }

  private prepareRequestHeaders(
    proxiedHeaders,
    additionalHeaders?: { [s: string]: string }
  ) {
    const filteredHeaders: { [s: string]: string } = { ...additionalHeaders };

    if (proxiedHeaders) {
      Object.keys(proxiedHeaders)
        .filter(key => this.REQUIRED_HEADERS.indexOf(key) >= 0)
        .map(key => {
          filteredHeaders[key] = proxiedHeaders[key];
        });
    }

    return filteredHeaders;
  }
}
