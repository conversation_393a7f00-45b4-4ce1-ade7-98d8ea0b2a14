import { BulkJob } from "../model/BulkJob";
import { Config } from "../config/config";
import {
  IGetOffersResponse,
  IGetOffersCounts,
  IDeleteOffersResponse
} from "../model/Offer";
import { Service } from "typedi";
import { REQUEST_HEADERS, apiErrorWrapper, ENV } from "../constants";
import {
  createOffersServiceMock,
  createBulkJobsMock,
  createCategoriesMock,
  createPromotionsMock
} from "../mocks/offersServiceMock";
import axios from "axios";
import Errors = require("restify-errors");
import { IGetCategoriesResponse } from "../model/Category";
import { IGetPromotionsResponse } from "../model/Promotion";

export const POST_API = {
  BULK_UPLOAD: "/offers/bulk",
  GET_OFFERS: "/offers",
  GET_OFFERS_COUNTS: "/offers/counts",
  BULK_PUBLISH_OFFERS: "/offers/bulk/publish",
  BULK_UPDATE_OFFERS: "/offers/bulk/",
  PUBLISH_OFFER: "/offers/publish",
  DELETE_OFFERS: "/offers/delete-multiple",
  DISABLE_OFFERS: "/offers/disable",
  ENABLE_OFFERS: "/offers/enable",
  GET_BULK_JOBS: "/offers/jobs",
  POST_OFFER: "/offers",
  PUT_OFFER: "/offers/",
  GET_CATEGORIES: "/categories",
  GET_PROMOTION: "/promotions"
};

@Service("OffersService")
export class OffersService {
  private REQUIRED_HEADERS: string[] = [REQUEST_HEADERS.USER_EMAIL];

  constructor(private config: Config) {
    if (this.config.config.get("useMockOffers")) {
      createOffersServiceMock();
    }
    if (this.config.config.get("useMockBulkJobs")) {
      createBulkJobsMock();
    }
    if (this.config.config.get("useMockCategories")) {
      createCategoriesMock();
    }
    if (this.config.config.get("useMockPromotions")) {
      createPromotionsMock();
    }
  }

  public async getOffersByFilters(req: any): Promise<IGetOffersResponse> {
    req.method = "get";
    return await this.makeEndpointCall(POST_API.GET_OFFERS, req);
  }

  public async getOffersCounts(req: any): Promise<IGetOffersCounts> {
    req.method = "get";
    return await this.makeEndpointCall(POST_API.GET_OFFERS_COUNTS, req);
  }

  public async getBulkJobs(req: any): Promise<BulkJob[]> {
    req.method = "get";
    return await this.makeEndpointCall(POST_API.GET_BULK_JOBS, req);
  }

  public async uploadBulkOffers(
    req: any
  ): Promise<{ [key: string]: { [key: string]: string } }> {
    return await this.makeEndpointCall(POST_API.BULK_UPLOAD, req, true);
  }

  /**
   * Publishes through the batch endpoint multiple offers by Id
   * // NOTE: this is not transactional on the Offers API side
   * @param req
   */
  public async bulkPublishOffers(req: any): Promise<any> {
    req.method = "post";

    return await this.makeEndpointCall(POST_API.BULK_PUBLISH_OFFERS, req, true);
  }

  /**
   * Publishes through the batch endpoint by Id
   * @param req
   */
  public async publishOffer(req: any): Promise<any> {
    req.method = "post";

    return await this.makeEndpointCall(POST_API.PUBLISH_OFFER, req, true);
  }

  /**
   * Proxies `Create new offer` request to Offers Management API
   * @param req request body that will be proxied to Offers Management API
   */
  public async postOffer(req: any): Promise<any> {
    req.method = "POST";

    return await this.makeEndpointCall(POST_API.POST_OFFER, req, true);
  }

  /**
   * Proxies `Update existing offer` request to Offers Management API
   * @param req request body that will be proxied to Offers Management API
   * @param offerId id of the offer that will be updated
   */
  public async putOffer(req: any, offerId: string): Promise<any> {
    req.method = "put";

    return await this.makeEndpointCall(POST_API.PUT_OFFER + offerId, req, true);
  }

  /**
   * Proxies `Update bulk` request through the batch endpoint to patch multiple offers by Id in a bulk job
   * @param req request body that will be proxied to Offers Management API
   * @param bulkId id of the bulk job that will be updated
   */
  public async bulkUpdateOffers(req: any, bulkId: string): Promise<any> {
    req.method = "patch";

    return await this.makeEndpointCall(
      POST_API.BULK_UPDATE_OFFERS + bulkId,
      req,
      true
    );
  }

  public async deleteOffers(req: any): Promise<IDeleteOffersResponse> {
    req.method = "post";

    return await this.makeEndpointCall(POST_API.DELETE_OFFERS, req);
  }

  public async disableOffer(req: any): Promise<any> {
    req.method = "post";

    return await this.makeEndpointCall(POST_API.DISABLE_OFFERS, req);
  }

  public async enableOffer(req: any): Promise<any> {
    req.method = "post";

    return await this.makeEndpointCall(POST_API.ENABLE_OFFERS, req);
  }

  public async getCategories(req: any): Promise<IGetCategoriesResponse> {
    req.method = "get";
    return await this.makeEndpointCall(POST_API.GET_CATEGORIES, req);
  }

  public async getPromotions(req: any): Promise<IGetPromotionsResponse> {
    req.method = "get";
    return await this.makeEndpointCall(POST_API.GET_PROMOTION, req);
  }

  /**
   * Allows us to call mock or actual endpoint
   * @param path the path on the offers endpoint to call
   */
  private async makeEndpointCall(
    path: string,
    req: any,
    skipQueryParams?: boolean,
    additionalHeaders?: { [s: string]: string }
  ) {
    try {
      // Make sure we send requests as id=1&id=2 instead of id=1,2 | only for query params that should be appended to the URL
      const values: { [key: string]: string[] } = {};
      if (req.query) {
        Object.keys(req.query).forEach(key => {
          if (req.query[key]) {
            values[key] = req.query[key].split(",");
          }
        });
      }

      const requestConfig = {
        url: `${this.config.config.get("offersEndpoint")}${path}`,
        headers: this.prepareRequestHeaders(req.headers, additionalHeaders),
        data: req.body,
        method: req.method,
        params: skipQueryParams ? undefined : values,
        paramsSerializer: {
          indexes: null // equivalent to indices: false in request
        }
      };

      // tslint:disable-next-line:no-console
      console.debug(
        "Making a request with params " + JSON.stringify(requestConfig)
      );

      const response = await axios(requestConfig);
      // tslint:disable-next-line:no-console
      console.debug("Response: " + JSON.stringify(response.data));
      return response.data;
    } catch (e) {
      this.throwRestifyError(e);
    }
  }

  private throwRestifyError(e) {
    // tslint:disable-next-line:no-console
    console.error(e);
    const error = apiErrorWrapper(e.response?.data || e.message);
    switch (e.response?.status) {
      case 400:
        throw new Errors.BadRequestError(error);
      case 503:
        throw new Errors.ServiceUnavailableError(error);
      case 504:
        throw new Errors.GatewayTimeoutError(error);
      default:
        throw new Errors.InternalServerError(error);
    }
  }

  private prepareRequestHeaders(
    proxiedHeaders,
    additionalHeaders?: { [s: string]: string }
  ) {
    const filteredHeaders: { [s: string]: string } = { ...additionalHeaders };

    if (proxiedHeaders) {
      Object.keys(proxiedHeaders)
        .filter(key => this.REQUIRED_HEADERS.indexOf(key) >= 0)
        .map(key => {
          filteredHeaders[key] = proxiedHeaders[key];
        });
    }

    return filteredHeaders;
  }
}
