import * as path from "path";
import { Partner } from "../model/Partner";
import { IApiResponse } from "../interface/IApiResponse";
import { Service } from "typedi";
import axios from "axios";
import { Config } from "../config/config";
// tslint:disable-next-line:no-var-requires
const MOCK_PARTNERS = require(path.join(
  __dirname,
  "../mockdata/partners.json"
));
import * as Errors from "restify-errors";
import { apiErrorWrapper, API_ERROR_CODES, ENV } from "../constants";

@Service("PartnersService")
export class PartnersService {
  constructor(private config: Config) {}

  /**
   * Filter getAllPartners as requested by <PERSON> for the POST tool
   * Only get Partners that are of type in-store.
   */
  public async getAllPartners(): Promise<Partner[]> {

    let partnerRequest = await this.makeEndpointCall(
      this.config.config.get("partnersEndpoint") + "?type=in-store&limit=10"
    );

    const results = partnerRequest.results;
    while (partnerRequest.limit + partnerRequest.offset < partnerRequest.total) {
      const offset = partnerRequest.limit + partnerRequest.offset;
      partnerRequest = await this.makeEndpointCall(
        this.config.config.get("partnersEndpoint") + "?type=in-store&limit=10" + "&offset="+ offset
      );
      results.push(...partnerRequest.results);
    }
    return results;
  }

  /**
   * Makes a call to the partners API to get partners by id, comma separated
   * @param ids
   */
  public async getPartnersByIds(ids: string[]): Promise<Partner[]> {
    const env = this.config.config.get("env");
    const endpoint =
        this.config.config.get("partnersEndpoint") +
        (env === ENV.LOCAL || env === ENV.DEVELOPMENT ? "" : "/" + ids.join(","));
    // Unfortunately, we need to separate this on environments because the partners endpoints is on the private VPN
    const response: IApiResponse = await this.makeEndpointCall(endpoint);
    return env === ENV.LOCAL || env === ENV.DEVELOPMENT
        ? response.results.filter(
            (partner: Partner) => ids.indexOf(partner.id) >= 0
        )
        : response.results;
  }

  /**
   * Check if partner exists
   * @param partnerId
   */
  public async doesPartnerExist(partnerId: string): Promise<Partner> {
    if (!partnerId) {
      throw new Errors.BadRequestError(
        apiErrorWrapper(API_ERROR_CODES.PARTNER_NOT_FOUND)
      );
    }

    const partners = await this.getPartnersByIds([partnerId]);
    if (!partners || partners.length === 0) {
      throw new Errors.NotFoundError(
        apiErrorWrapper(API_ERROR_CODES.PARTNER_NOT_FOUND)
      );
    }

    return partners[0];
  }

  /**
   * Allows us to split up the partner service endpoint calls to different locations depending on environment
   * due to limited access to the VPN on outside network access
   * @param endpoint the endpoint to call
   */
  private makeEndpointCall(endpoint: string): Promise<IApiResponse> {
    const env = this.config.config.get("env");
    if (env !== ENV.LOCAL && env !== ENV.DEVELOPMENT) {
      return axios.get(endpoint).then(response => response.data);
    } else {
      return Promise.resolve(MOCK_PARTNERS);
    }
  }
}
