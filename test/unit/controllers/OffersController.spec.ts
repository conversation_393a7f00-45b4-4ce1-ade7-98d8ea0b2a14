import * as _ from "lodash";
import * as sinon from "sinon";

import {
  API_ERROR_CODES,
  REQUEST_HEADERS,
  apiErrorWrapper
} from "../../../src/constants";
import { assert, expect } from "chai";

import { BulkJob } from "../../../src/model/BulkJob";
import { Group } from "../../../src/entity/Group";
import { OffersController } from "../../../src/controllers/OffersController";
import { Partner } from "../../../src/model/Partner";
import { User } from "../../../src/entity/User";

import * as Errors from "restify-errors";
import { IOfferFormModel, ICopyText } from "../../../src/model/Offer";

describe("OffersController", () => {
  let offersController: OffersController;
  let groupsService: any;
  let usersService: any;
  let offersService: any;
  let contentService: any;
  let offerDeliveryService: any;
  let req: any;
  let res: any;
  let next: any;

  before(() => {
    groupsService = sinon.fake();
    usersService = sinon.fake();
    offersService = sinon.fake();
    contentService = sinon.fake();
    offerDeliveryService = sinon.fake();
    offersController = new OffersController(
      usersService,
      offersService,
      groupsService,
      contentService,
      offerDeliveryService
    );
  });

  describe("Get offers by filters", () => {
    let userId;
    let user: User;
    let partners: Partner[];
    let partnerId: string;
    let pageSize: number;
    let pageNumber: number;
    let totalCount: number;
    let getOffersResponse;
    let groups: Group[];
    beforeEach(() => {
      userId = "<EMAIL>";
      partnerId = "partnerId";
      user = {
        id: userId,
        partners: [{ partnerId, userId }]
      };
      partners = [
        {
          id: partnerId
        }
      ];
      getOffersResponse = {
        totalCount,
        pageSize,
        pageNumber,
        content: [
          {
            partnerId
          }
        ]
      };
      groups = [
        {
          id: 1,
          name: "Admin",
          coversAllPartners: true,
          isEditable: false
        }
      ];
      req = {
        header: headerName => {
          return headerName === REQUEST_HEADERS.USER_EMAIL ? userId : null;
        }
      };
      res = sinon.fake();
      res.send = sinon.fake();
      next = sinon.fake();

      groupsService.getGroupsByUser = sinon.fake.returns(groups);
      usersService.doesUserExist = sinon.fake.returns(user);
      usersService.getPartnersForUser = sinon.fake.returns(partners);
      offersService.getOffersByFilters = sinon.fake.returns(getOffersResponse);
    });

    it("Should retrieve all offers for an administrator", done => {
      res.send = response => {
        expect(usersService.doesUserExist.calledOnce).equals(true);
        expect(usersService.getPartnersForUser.calledOnce).equals(true);
        expect(offersService.getOffersByFilters.calledOnce).equals(true);
        expect(response.content.length).equals(1);
        done();
      };
      offersController.getOffersByFilters(req, res, next);
    });

    it("Should throw an error if no user is found", done => {
      usersService.doesUserExist = sinon.fake.throws(
        new Errors.BadRequestError(
          apiErrorWrapper(API_ERROR_CODES.USER_NOT_FOUND)
        )
      );
      offersController.getOffersByFilters(req, res, next).catch(error => {
        expect(error.context.code).to.equal(
          API_ERROR_CODES.USER_NOT_FOUND.code
        );
        done();
      });
    });

    it("Should throw an error if a non admin user passes a partnerId it doesnt have access to", done => {
      req = {
        header: headerName => {
          return headerName === REQUEST_HEADERS.USER_EMAIL ? userId : null;
        },
        params: {
          partnerId: "another Id"
        }
      };
      usersService.getPartnersForUser = sinon.fake.returns([]);
      groupsService.getGroupsByUser = sinon.fake.returns([]);

      offersController.getOffersByFilters(req, res, next).catch(error => {
        expect(error.context.code).to.equal(
          API_ERROR_CODES.PARTNER_NOT_ASSIGNED_TO_USER.code
        );
        done();
      });
    });

    it("Should return no offers if the user has no partners and is not an admin", done => {
      groups = [
        {
          id: 1,
          name: "non-admin",
          coversAllPartners: false,
          isEditable: false
        }
      ];
      offersService.getOffersByFilters = sinon.fake.returns({ content: [] });
      usersService.getPartnersForUser = sinon.fake.returns([]);
      res.send = response => {
        expect(usersService.doesUserExist.calledOnce).equals(true);
        expect(usersService.getPartnersForUser.calledOnce).equals(true);
        expect(offersService.getOffersByFilters.calledOnce).equals(true);
        expect(response.content.length).equals(0);
        done();
      };
      offersController.getOffersByFilters(req, res, next);
    });
  });

  describe("Bulk Upload Offers", () => {
    let userId;
    let groupId;
    let user;
    let partners;
    let partnerId;
    let groups;
    let partnerInGroupId;
    let offers;
    let bulkId;

    beforeEach(() => {
      userId = "<EMAIL>";
      groupId = 1234;
      partnerId = "partnerId";
      partnerInGroupId = "partnerInGroupId";
      user = {
        id: userId,
        partners: [{ partnerId, userId }]
      };
      partners = [
        {
          id: partnerId
        }
      ];
      groups = [
        {
          partnerMappings: [{ partnerId }, { partnerId: partnerInGroupId }]
        }
      ];
      offers = [
        {
          partnerId
        }
      ];
      req = {
        header: headerName => {
          return headerName === REQUEST_HEADERS.USER_EMAIL ? userId : null;
        },
        body: { offers }
      };
      bulkId = "bulkId";
      res = sinon.fake();
      res.send = sinon.fake();
      next = sinon.fake();

      usersService.doesUserExist = sinon.fake.returns(user);
      groupsService.getGroupsByUser = sinon.fake.returns(groups);
      offersService.uploadBulkOffers = sinon.fake.returns({ bulkId });
    });

    it("should return success for successful upload, partner in group assigned to user", done => {
      req.body = {
        offers: [
          {
            partnerId: partnerInGroupId
          }
        ]
      };

      res.send = response => {
        expect(usersService.doesUserExist.calledOnce).equals(true);
        expect(groupsService.getGroupsByUser.calledOnce).equals(true);
        expect(offersService.uploadBulkOffers.calledOnce).equals(true);
        expect(response.bulkId).equals(bulkId);
        done();
      };

      offersController.onBulkUploadOffers(req, res, next);
    });

    it("Should throw an error if null body", done => {
      req.body = null;
      offersController.onBulkUploadOffers(req, res, next).catch(error => {
        expect(error.context.code).to.equal(
          API_ERROR_CODES.BULK_ATLEAST_ONE_OFFER.code
        );
        done();
      });
    });

    it("Should throw an error if null offers", done => {
      req.body = {
        offers: null
      };
      offersController.onBulkUploadOffers(req, res, next).catch(error => {
        expect(error.context.code).to.equal(
          API_ERROR_CODES.BULK_ATLEAST_ONE_OFFER.code
        );
        done();
      });
    });

    it("Should throw an error if 0 offers", done => {
      req.body = {
        offers: []
      };
      offersController.onBulkUploadOffers(req, res, next).catch(error => {
        expect(error.context.code).to.equal(
          API_ERROR_CODES.BULK_ATLEAST_ONE_OFFER.code
        );
        done();
      });
    });

    it("Should throw an error if muliple partners in offers list", done => {
      req.body = {
        offers: [{ partnerId }, { partnerId: "partnerId2" }]
      };
      offersController.onBulkUploadOffers(req, res, next).catch(error => {
        expect(error.context.code).to.equal(
          API_ERROR_CODES.BULK_ONLY_ONE_PARTNER.code
        );
        done();
      });
    });

    it("Should throw an error if partner is not assigned to user", done => {
      req.body = {
        offers: [{ partnerId: "partnerId3" }]
      };
      offersController.onBulkUploadOffers(req, res, next).catch(error => {
        expect(error.context.code).to.equal(
          API_ERROR_CODES.PARTNER_NOT_ASSIGNED_TO_USER.code
        );
        done();
      });
    });
  });

  describe("Bulk Update Offers", () => {
    let userId;
    let groupId;
    let user;
    let partners;
    let partnerId;
    let groups;
    let partnerInGroupId;
    let offers;
    let bulkId;

    beforeEach(() => {
      userId = "<EMAIL>";
      groupId = 1234;
      partnerId = "partnerId";
      partnerInGroupId = "partnerInGroupId";
      bulkId = "bulkId";
      user = {
        id: userId,
        partners: [{ partnerId, userId }]
      };
      partners = [
        {
          id: partnerId
        }
      ];
      groups = [
        {
          partnerMappings: [{ partnerId }, { partnerId: partnerInGroupId }]
        }
      ];
      offers = [
        {
          partnerId
        }
      ];
      req = {
        header: headerName => {
          return headerName === REQUEST_HEADERS.USER_EMAIL ? userId : null;
        },
        params: {
          bulkId
        },
        body: { offers }
      };
      res = sinon.fake();
      res.send = sinon.fake();
      next = sinon.fake();

      groupsService.getGroupsByUser = sinon.fake.returns(groups);
      usersService.doesUserExist = sinon.fake.returns(user);
      offersService.bulkUpdateOffers = sinon.fake.returns({});
    });

    it("should throw an error when the bulk id is not found", done => {
      req.params = {
        bulkId: null
      };
      offersController.bulkUpdateOffers(req, res, next).catch(error => {
        expect(error.context.code).to.equal(
          API_ERROR_CODES.MISSING_BULK_ID.code
        );
        done();
      });
    });

    it("should update all offers", done => {
      res.send = response => {
        expect(usersService.doesUserExist.calledOnce).equals(true);
        expect(groupsService.getGroupsByUser.calledOnce).equals(true);
        expect(offersService.bulkUpdateOffers.calledOnce).equals(true);
        done();
      };
      offersController.bulkUpdateOffers(req, res, next);
    });

    it("Should throw an error if no user is found", done => {
      usersService.doesUserExist = sinon.fake.throws(
        new Errors.BadRequestError(
          apiErrorWrapper(API_ERROR_CODES.USER_NOT_FOUND)
        )
      );
      offersController.bulkUpdateOffers(req, res, next).catch(error => {
        expect(error.context.code).to.equal(
          API_ERROR_CODES.USER_NOT_FOUND.code
        );
        done();
      });
    });

    it("Should throw an error if null body", done => {
      req.body = null;
      offersController.bulkUpdateOffers(req, res, next).catch(error => {
        expect(error.context.code).to.equal(
          API_ERROR_CODES.BULK_ATLEAST_ONE_OFFER.code
        );
        done();
      });
    });

    it("Should throw an error if null offers", done => {
      req.body = {
        offers: null
      };
      offersController.bulkUpdateOffers(req, res, next).catch(error => {
        expect(error.context.code).to.equal(
          API_ERROR_CODES.BULK_ATLEAST_ONE_OFFER.code
        );
        done();
      });
    });

    it("Should throw an error if 0 offers", done => {
      req.body = {
        offers: []
      };
      offersController.bulkUpdateOffers(req, res, next).catch(error => {
        expect(error.context.code).to.equal(
          API_ERROR_CODES.BULK_ATLEAST_ONE_OFFER.code
        );
        done();
      });
    });

    it("Should throw an error if muliple partners in offers list", done => {
      req.body = {
        offers: [{ partnerId }, { partnerId: "partnerId2" }]
      };
      offersController.bulkUpdateOffers(req, res, next).catch(error => {
        expect(error.context.code).to.equal(
          API_ERROR_CODES.BULK_ONLY_ONE_PARTNER.code
        );
        done();
      });
    });

    it("Should throw an error if partner is not assigned to user", done => {
      req.body = {
        offers: [{ partnerId: "partnerId3" }]
      };
      offersController.bulkUpdateOffers(req, res, next).catch(error => {
        expect(error.context.code).to.equal(
          API_ERROR_CODES.PARTNER_NOT_ASSIGNED_TO_USER.code
        );
        done();
      });
    });
  });

  describe("Bulk Publish offers", () => {
    let userId;
    let user: User;
    let partnerId: string;
    let offers;
    let pageSize: number;
    let pageNumber: number;
    let totalCount: number;
    let getOffersResponse;
    let groups: Group[];
    const offerId: string = "offerId";
    beforeEach(() => {
      userId = "<EMAIL>";
      partnerId = "partnerId";
      user = {
        id: userId,
        partners: [{ partnerId, userId }]
      };
      getOffersResponse = {
        totalCount,
        pageSize,
        pageNumber,
        content: [
          {
            id: offerId,
            partnerId
          }
        ]
      };
      groups = [
        {
          id: 1,
          name: "non-Admin",
          coversAllPartners: false,
          isEditable: false
        }
      ];
      req = {
        header: headerName => {
          return headerName === REQUEST_HEADERS.USER_EMAIL ? userId : null;
        },
        body: { id: offerId },
        query: {},
        params: {}
      };
      res = sinon.fake();
      res.send = sinon.fake();
      next = sinon.fake();

      groupsService.getGroupsByUser = sinon.fake.returns(groups);
      usersService.doesUserExist = sinon.fake.returns(user);
      offersService.getOffersByFilters = sinon.fake.returns(getOffersResponse);
      offersService.bulkPublishOffers = sinon.fake.returns({});
    });

    it("should publish all offers", done => {
      res.send = response => {
        expect(usersService.doesUserExist.calledOnce).equals(true);
        expect(groupsService.getGroupsByUser.calledOnce).equals(true);
        expect(offersService.getOffersByFilters.calledOnce).equals(true);
        expect(offersService.bulkPublishOffers.calledOnce).equals(true);
        done();
      };
      offersController.bulkPublishOffers(req, res, next);
    });

    it("Should throw an error if no user is found", done => {
      usersService.doesUserExist = sinon.fake.throws(
        new Errors.BadRequestError(
          apiErrorWrapper(API_ERROR_CODES.USER_NOT_FOUND)
        )
      );
      offersController.bulkPublishOffers(req, res, next).catch(error => {
        expect(error.context.code).to.equal(
          API_ERROR_CODES.USER_NOT_FOUND.code
        );
        done();
      });
    });

    it("Should throw an error if a non admin user passes an offerId it doesnt have access to", done => {
      usersService.getPartnersForUser = sinon.fake.returns([]);
      groupsService.getGroupsByUser = sinon.fake.returns([]);
      offersService.getOffersByFilters = sinon.fake.returns({
        content: [
          {
            id: offerId,
            partnerId: "another partner Id"
          }
        ]
      });

      offersController.bulkPublishOffers(req, res, next).catch(error => {
        expect(error.context.code).to.equal(
          API_ERROR_CODES.PARTNER_NOT_ASSIGNED_TO_USER.code
        );
        done();
      });
    });

    it("Should throw an error if no offers were found with the ids", done => {
      offersService.getOffersByFilters = sinon.fake.returns({ content: [] });

      offersController.bulkPublishOffers(req, res, next).catch(error => {
        expect(error.context.code).to.equal(
          API_ERROR_CODES.OFFER_NOT_FOUND.code
        );
        done();
      });
    });

    it("Should throw an error if some offers were not found with the ids", done => {
      offersService.getOffersByFilters = sinon.fake.returns({
        content: [
          {
            offerId: "a different offerId"
          }
        ]
      });
      offersController.bulkPublishOffers(req, res, next).catch(error => {
        expect(error.context.code).to.equal(
          API_ERROR_CODES.OFFER_NOT_FOUND.code
        );
        done();
      });
    });
  });

  describe("Single Offer Publish", () => {
    let userId;
    let user: User;
    let partnerId: string;
    let offers;
    let pageSize: number;
    let pageNumber: number;
    let totalCount: number;
    let getOffersResponse;
    let groups: Group[];
    const offerId: string = "offerId";
    beforeEach(() => {
      userId = "<EMAIL>";
      partnerId = "partnerId";
      user = {
        id: userId,
        partners: [{ partnerId, userId }]
      };
      getOffersResponse = {
        totalCount,
        pageSize,
        pageNumber,
        content: [
          {
            id: offerId,
            partnerId
          }
        ]
      };
      groups = [
        {
          id: 1,
          name: "non-Admin",
          coversAllPartners: false,
          isEditable: false
        }
      ];
      req = {
        header: headerName => {
          return headerName === REQUEST_HEADERS.USER_EMAIL ? userId : null;
        },
        body: { id: offerId },
        params: {},
        query: {}
      };
      res = sinon.fake();
      res.send = sinon.fake();
      next = sinon.fake();

      groupsService.getGroupsByUser = sinon.fake.returns(groups);
      usersService.doesUserExist = sinon.fake.returns(user);
      offersService.getOffersByFilters = sinon.fake.returns(getOffersResponse);
      offersService.publishOffer = sinon.fake.returns({});
    });

    it("should publish all offers", done => {
      res.send = response => {
        expect(usersService.doesUserExist.calledOnce).equals(true);
        expect(groupsService.getGroupsByUser.calledOnce).equals(true);
        expect(offersService.getOffersByFilters.calledOnce).equals(true);
        expect(offersService.publishOffer.calledOnce).equals(true);
        done();
      };
      offersController.publishOffer(req, res, next);
    });

    it("Should throw an error if no user is found", done => {
      usersService.doesUserExist = sinon.fake.throws(
        new Errors.BadRequestError(
          apiErrorWrapper(API_ERROR_CODES.USER_NOT_FOUND)
        )
      );
      offersController.publishOffer(req, res, next).catch(error => {
        expect(error.context.code).to.equal(
          API_ERROR_CODES.USER_NOT_FOUND.code
        );
        done();
      });
    });

    it("Should throw an error if a non admin user passes an offerId it doesnt have access to", done => {
      usersService.getPartnersForUser = sinon.fake.returns([]);
      groupsService.getGroupsByUser = sinon.fake.returns([]);
      offersService.getOffersByFilters = sinon.fake.returns({
        content: [
          {
            id: offerId,
            partnerId: "another partner Id"
          }
        ]
      });

      offersController.publishOffer(req, res, next).catch(error => {
        expect(error.context.code).to.equal(
          API_ERROR_CODES.PARTNER_NOT_ASSIGNED_TO_USER.code
        );
        done();
      });
    });

    it("Should throw an error if no offers were found with the ids", done => {
      offersService.getOffersByFilters = sinon.fake.returns({ content: [] });

      offersController.publishOffer(req, res, next).catch(error => {
        expect(error.context.code).to.equal(
          API_ERROR_CODES.OFFER_NOT_FOUND.code
        );
        done();
      });
    });

    it("Should throw an error if some offers were not found with the ids", done => {
      offersService.getOffersByFilters = sinon.fake.returns({
        content: [
          {
            offerId: "a different offerId"
          }
        ]
      });
      offersController.publishOffer(req, res, next).catch(error => {
        expect(error.context.code).to.equal(
          API_ERROR_CODES.OFFER_NOT_FOUND.code
        );
        done();
      });
    });
  });

  describe("Get bulk jobs", () => {
    let userId;
    let user: User;
    let partnerId: string;
    let partnerIdNotAssignedToUser: string;
    let groups: Group[];
    let bulkJobs: BulkJob[];
    beforeEach(() => {
      userId = "<EMAIL>";
      partnerId = "partnerId";
      partnerIdNotAssignedToUser = "partnerIdNotAssignedToUser";
      user = {
        id: userId,
        partners: [{ partnerId, userId }]
      };
      groups = [
        {
          id: 1,
          name: "Admin",
          coversAllPartners: true,
          isEditable: false
        }
      ];
      bulkJobs = [
        {
          id: "bulkId1",
          name: "Bulk Job 1",
          partnerId
        },
        {
          id: "bulkId2",
          name: "Bulk Job 2",
          partnerId: partnerIdNotAssignedToUser
        }
      ];
      req = {
        header: headerName => {
          return headerName === REQUEST_HEADERS.USER_EMAIL ? userId : null;
        }
      };
      res = sinon.fake();
      res.send = sinon.fake();
      next = sinon.fake();

      groupsService.getGroupsByUser = sinon.fake.returns(groups);
      usersService.doesUserExist = sinon.fake.returns(user);
      offersService.getBulkJobs = sinon.fake.returns(bulkJobs);
    });

    it("Should retrieve all bulk jobs for an administrator", done => {
      res.send = response => {
        expect(usersService.doesUserExist.calledOnce).equals(true);
        expect(groupsService.getGroupsByUser.calledOnce).equals(true);
        expect(offersService.getBulkJobs.calledOnce).equals(true);
        expect(response.length).equals(bulkJobs.length);
        done();
      };
      offersController.getBulkJobs(req, res, next);
    });

    it("Should return only bulk jobs for partners assigned to a non-admin user", done => {
      // User is already assigned to partnerId directly
      groups = [
        {
          id: 1,
          name: "non-admin",
          coversAllPartners: false,
          isEditable: false
        }
      ];
      groupsService.getGroupsByUser = sinon.fake.returns(groups);
      res.send = response => {
        // Should only return 1 bulk job for the partner assigned to the user
        expect(response.length).equals(1);
        expect(response[0].partnerId).equals(partnerId);
        done();
      };
      offersController.getBulkJobs(req, res, next);
    });
  });

  describe("Get a bulk job by id", () => {
    let userId;
    let user: User;
    let partnerId: string;
    let partnerIdNotAssignedToUser: string;
    let groups: Group[];
    let bulkJobs: BulkJob[];
    let bulkJob: BulkJob;
    const bulkId: string = "bulkId2";

    beforeEach(() => {
      userId = "<EMAIL>";
      partnerId = "partnerId";
      partnerIdNotAssignedToUser = "partnerIdNotAssignedToUser";
      user = {
        id: userId,
        partners: [{ partnerId, userId }]
      };
      groups = [
        {
          id: 1,
          name: "Admin",
          coversAllPartners: true,
          isEditable: false
        }
      ];
      bulkJobs = [
        {
          id: "bulkId1",
          name: "Bulk Job 1",
          partnerId
        },
        {
          id: "bulkId2",
          name: "Bulk Job 2",
          partnerId: partnerIdNotAssignedToUser
        }
      ];
      bulkJob = {
        id: "bulkId2",
        name: "Bulk Job 2",
        partnerId: partnerIdNotAssignedToUser
      };
      req = {
        header: headerName => {
          return headerName === REQUEST_HEADERS.USER_EMAIL ? userId : null;
        },
        params: { bulkId }
      };
      res = sinon.fake();
      res.send = sinon.fake();
      next = sinon.fake();

      groupsService.getGroupsByUser = sinon.fake.returns(groups);
      usersService.doesUserExist = sinon.fake.returns(user);
      offersService.getBulkJobs = sinon.fake.returns(bulkJobs);
      offersService.getBulkJobById = sinon.fake.returns(bulkJob);
    });

    it("Should retrieve all bulk jobs for an administrator", done => {
      res.send = response => {
        expect(usersService.doesUserExist.calledOnce).equals(true);
        expect(groupsService.getGroupsByUser.calledOnce).equals(true);
        expect(offersService.getBulkJobs.calledOnce).equals(true);
        expect(response.length).equals(bulkJobs.length);
        done();
      };
      offersController.getBulkJobs(req, res, next);
    });

    it("Should return only the bulk job that is specified in the id", done => {
      res.send = response => {
        expect(response.id).equals(bulkId);
        done();
      };
      offersController.getBulkJobById(req, res, next);
    });

    it("Should throw an error if there is no bulk id set", done => {
      req = {
        header: headerName => {
          return headerName === REQUEST_HEADERS.USER_EMAIL ? userId : null;
        },
        params: { bulkId: "" }
      };

      offersController.getBulkJobById(req, res, next).catch(error => {
        expect(error.context.code).to.equal(
          API_ERROR_CODES.BULK_JOB_NOT_FOUND.code
        );
        done();
      });
    });
    it("Should throw an error if there is no bulk id found", done => {
      req = {
        header: headerName => {
          return headerName === REQUEST_HEADERS.USER_EMAIL ? userId : null;
        },
        params: { bulkId: "badId" }
      };

      offersController.getBulkJobById(req, res, next).catch(error => {
        expect(error.context.code).to.equal(
          API_ERROR_CODES.BULK_JOB_NOT_FOUND.code
        );
        done();
      });
    });
  });

  describe("Generate Content for offers", () => {
    let partnerId: string;
    const copyText: ICopyText = require("./../../../src/mockdata/sample_copy_content.json");
    beforeEach(() => {
      partnerId = "partnerId";
      req = {
        body: {
          id: "1"
        }
      };

      res = sinon.fake();
      res.send = sinon.fake();
      next = sinon.fake();

      contentService.generateCopyTextContent = sinon.fake.returns(copyText);
    });

    it("Should return an offer with generated copy text", done => {
      res.send = response => {
        expect(contentService.generateCopyTextContent.calledOnce).equals(true);
        done();
      };
      offersController.generateContent(req, res, next);
    });

    it("should throw an error if no offer is passed", done => {
      req.body = null;
      offersController.generateContent(req, res, next).catch(error => {
        expect(error.context.code).to.equal(
          API_ERROR_CODES.OFFER_REQUIRED.code
        );
        done();
      });
    });
  });

  describe("Create offers", () => {
    let userId;
    let user: User;
    let partnerId: string;
    let getOffersResponse;
    let groups: Group[];
    const offerId: string = "offerId";
    beforeEach(() => {
      userId = "<EMAIL>";
      partnerId = "partnerId";
      user = {
        id: userId,
        partners: [{ partnerId, userId }]
      };
      groups = [
        {
          id: 1,
          name: "non-Admin",
          coversAllPartners: false,
          isEditable: false
        }
      ];
      req = {
        header: headerName => {
          return headerName === REQUEST_HEADERS.USER_EMAIL ? userId : null;
        },
        body: { partnerId },
        params: { offerId }
      };
      res = sinon.fake();
      res.send = sinon.fake();
      next = sinon.fake();

      groupsService.getGroupsByUser = sinon.fake.returns(groups);
      usersService.doesUserExist = sinon.fake.returns(user);
      offersService.getOffersByFilters = sinon.fake.returns(getOffersResponse);
      offersService.postOffer = sinon.fake.returns({});
    });

    it("should create the offer", done => {
      res.send = response => {
        expect(usersService.doesUserExist.calledOnce).equals(true);
        expect(offersService.postOffer.calledOnce).equals(true);
        done();
      };
      offersController.postOffer(req, res, next);
    });

    it("Should throw an error if no user is found", done => {
      usersService.doesUserExist = sinon.fake.throws(
        new Errors.BadRequestError(
          apiErrorWrapper(API_ERROR_CODES.USER_NOT_FOUND)
        )
      );
      offersController.postOffer(req, res, next).catch(error => {
        expect(error.context.code).to.equal(
          API_ERROR_CODES.USER_NOT_FOUND.code
        );
        done();
      });
    });

    it("Should throw an error if a non admin user passes a partnerId it doesnt have access to", done => {
      usersService.getPartnersForUser = sinon.fake.returns([]);
      groupsService.getGroupsByUser = sinon.fake.returns([]);

      req = {
        header: headerName => {
          return headerName === REQUEST_HEADERS.USER_EMAIL ? userId : null;
        },
        body: { partnerId: "badPartner" },
        params: { offerId }
      };

      offersController.postOffer(req, res, next).catch(error => {
        expect(error.context.code).to.equal(
          API_ERROR_CODES.PARTNER_NOT_ASSIGNED_TO_USER.code
        );
        done();
      });
    });

    it("Should throw an error if no partnerId is provided", done => {
      usersService.getPartnersForUser = sinon.fake.returns([]);
      groupsService.getGroupsByUser = sinon.fake.returns([]);

      req = {
        header: headerName => {
          return headerName === REQUEST_HEADERS.USER_EMAIL ? userId : null;
        },
        body: {},
        params: { offerId }
      };

      offersController.postOffer(req, res, next).catch(error => {
        expect(error.context.code).to.equal(
          API_ERROR_CODES.UNSPECIFIED_PARTNER.code
        );
        done();
      });
    });
  });

  describe("Edit offers", () => {
    let userId;
    let user: User;
    let partnerId: string;
    let getOffersResponse;
    let groups: Group[];
    const offerId: string = "offerId";
    beforeEach(() => {
      userId = "<EMAIL>";
      partnerId = "partnerId";
      user = {
        id: userId,
        partners: [{ partnerId, userId }]
      };
      groups = [
        {
          id: 1,
          name: "non-Admin",
          coversAllPartners: false,
          isEditable: false
        }
      ];
      req = {
        header: headerName => {
          return headerName === REQUEST_HEADERS.USER_EMAIL ? userId : null;
        },
        body: { partnerId },
        params: { offerId }
      };
      res = sinon.fake();
      res.send = sinon.fake();
      next = sinon.fake();

      groupsService.getGroupsByUser = sinon.fake.returns(groups);
      usersService.doesUserExist = sinon.fake.returns(user);
      offersService.getOffersByFilters = sinon.fake.returns(getOffersResponse);
      offersService.postOffer = sinon.fake.returns({});
      offersService.putOffer = sinon.fake.returns({});
    });

    it("should create the offer", done => {
      res.send = response => {
        expect(usersService.doesUserExist.calledOnce).equals(true);
        expect(offersService.putOffer.calledOnce).equals(true);
        done();
      };
      offersController.putOffer(req, res, next);
    });

    it("Should throw an error if no user is found", done => {
      usersService.doesUserExist = sinon.fake.throws(
        new Errors.BadRequestError(
          apiErrorWrapper(API_ERROR_CODES.USER_NOT_FOUND)
        )
      );
      offersController.putOffer(req, res, next).catch(error => {
        expect(error.context.code).to.equal(
          API_ERROR_CODES.USER_NOT_FOUND.code
        );
        done();
      });
    });

    it("Should throw an error if a non admin user passes a partnerId it doesnt have access to", done => {
      usersService.getPartnersForUser = sinon.fake.returns([]);
      groupsService.getGroupsByUser = sinon.fake.returns([]);

      req = {
        header: headerName => {
          return headerName === REQUEST_HEADERS.USER_EMAIL ? userId : null;
        },
        body: { partnerId: "badPartner" },
        params: { offerId }
      };

      offersController.putOffer(req, res, next).catch(error => {
        expect(error.context.code).to.equal(
          API_ERROR_CODES.PARTNER_NOT_ASSIGNED_TO_USER.code
        );
        done();
      });
    });

    it("Should throw an error if no partnerId is provided", done => {
      usersService.getPartnersForUser = sinon.fake.returns([]);
      groupsService.getGroupsByUser = sinon.fake.returns([]);

      req = {
        header: headerName => {
          return headerName === REQUEST_HEADERS.USER_EMAIL ? userId : null;
        },
        body: {},
        params: { offerId }
      };

      offersController.putOffer(req, res, next).catch(error => {
        expect(error.context.code).to.equal(
          API_ERROR_CODES.UNSPECIFIED_PARTNER.code
        );
        done();
      });
    });

    it("Should throw an error if no offerId is provided", done => {
      usersService.getPartnersForUser = sinon.fake.returns([]);
      groupsService.getGroupsByUser = sinon.fake.returns([]);

      req = {
        header: headerName => {
          return headerName === REQUEST_HEADERS.USER_EMAIL ? userId : null;
        },
        body: { partnerId },
        params: {}
      };

      offersController.putOffer(req, res, next).catch(error => {
        expect(error.context.code).to.equal(
          API_ERROR_CODES.MISSING_OFFER_ID.code
        );
        done();
      });
    });
  });
});
