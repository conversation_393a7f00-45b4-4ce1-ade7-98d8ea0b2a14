import { expect } from "chai";
import * as sinon from "sinon";
import * as errors from "restify-errors";
import { ORM_ERRORS } from "../../../src/errors/orm.errors";
import Container from "typedi";
import { UsersService } from "../../../src/services/users.service";
import { UserGroup } from "../../../src/entity/UserGroup";
import { UserPartnerDirect } from "../../../src/entity/UserPartnerDirect";
import { User } from "../../../src/entity/User";
import { API_ERROR_CODES } from "../../../src/constants";
import { Group } from "../../../src/entity/Group";
import { Partner } from "../../../src/model/Partner";

describe("UsersService", () => {
  let usersService: UsersService;
  let userRepository;
  let userGroupRepository;
  let userPartnerDirectRepository;
  let groupsService;
  let partnersService;
  const requestingUserId: string = "";

  before(() => {
    userRepository = sinon.fake();
    userGroupRepository = sinon.fake();
    userPartnerDirectRepository = sinon.fake();
    groupsService = sinon.fake();
    partnersService = sinon.fake();
    Container.set("UserRepository", userRepository);
    Container.set("UserGroupRepository", userGroupRepository);
    Container.set("UserPartnerDirectRepository", userPartnerDirectRepository);
    Container.set("GroupsService", groupsService);
    Container.set("PartnersService", partnersService);
    usersService = Container.get(UsersService);
  });

  describe("Get User Id", () => {
    it("should add a group to user if not already there", async () => {
      const userId = "<EMAIL>";
      const responseUser = new User();
      userRepository.findOne = sinon.fake.returns(responseUser);
      const getUser = await usersService.getUserById(userId);
      expect(userRepository.findOne.calledOnce).equals(true);
      expect(getUser.id).to.equal(responseUser.id);
    });
  });

  describe("Add Group to User by Id", () => {
    const groupId = 1;
    const userId = "<EMAIL>";
    const responseGroup = new UserGroup();

    beforeEach(() => {
      responseGroup.userId = userId;
      responseGroup.groupId = groupId;
      userGroupRepository.create = sinon.fake.returns(new UserGroup());
      userGroupRepository.save = sinon.fake.returns(responseGroup);
    });

    it("should add a group to user if not already there", async () => {
      const newGroup = await usersService.addGroupToUser(
        userId,
        groupId,
        requestingUserId
      );
      expect(userGroupRepository.create.calledOnce).equals(true);
      expect(userGroupRepository.save.calledOnce).equals(true);
      expect(newGroup.userId).to.equal(responseGroup.userId);
    });

    it("should throw a conflict error if the group already exists", done => {
      userGroupRepository.save = sinon.stub().throws({
        code: ORM_ERRORS.DUPLICATE_ENTRY
      });

      usersService
        .addGroupToUser(userId, groupId, requestingUserId)
        .catch(error => {
          expect(error).instanceOf(errors.ConflictError);
          done();
        });
    });

    it("should throw a internal server error in other cases", done => {
      userGroupRepository.save = sinon.stub().throws(new Error());

      usersService
        .addGroupToUser(userId, groupId, requestingUserId)
        .catch(error => {
          expect(error).instanceOf(errors.InternalError);
          done();
        });
    });
  });

  describe("Add Partner to User by Id", () => {
    const partnerId = "partnerID";
    const userId = "<EMAIL>";
    const responseUserPartnerDirect = new UserPartnerDirect();

    beforeEach(() => {
      responseUserPartnerDirect.userId = userId;
      responseUserPartnerDirect.partnerId = partnerId;
      userPartnerDirectRepository.create = sinon.fake.returns(new UserGroup());
      userPartnerDirectRepository.save = sinon.fake.returns(
        responseUserPartnerDirect
      );
    });

    it("should add a partner to user if not already there", async () => {
      const newUserPartnerDirect = await usersService.addPartnerToUser(
        userId,
        partnerId,
        requestingUserId
      );
      expect(userPartnerDirectRepository.create.calledOnce).equals(true);
      expect(userPartnerDirectRepository.save.calledOnce).equals(true);
      expect(newUserPartnerDirect.userId).to.equal(
        responseUserPartnerDirect.userId
      );
    });

    it("should throw a conflict error if the partner already exists", done => {
      userPartnerDirectRepository.save = sinon.stub().throws({
        code: ORM_ERRORS.DUPLICATE_ENTRY
      });

      usersService
        .addPartnerToUser(userId, partnerId, requestingUserId)
        .catch(error => {
          expect(error).instanceOf(errors.ConflictError);
          done();
        });
    });

    it("should throw a internal server error in other cases", done => {
      userPartnerDirectRepository.save = sinon.stub().throws(new Error());

      usersService
        .addPartnerToUser(userId, partnerId, requestingUserId)
        .catch(error => {
          expect(error).instanceOf(errors.InternalError);
          done();
        });
    });
  });

  describe("Does user exist", () => {
    const userId = "100001";

    it("Should throw an error if no userId is passed", done => {
      usersService.doesUserExist(null).catch(error => {
        expect(error.context.code).to.equal(
          API_ERROR_CODES.USER_NOT_FOUND.code
        );
        done();
      });
    });

    it("Should throw an error if invalid userId is passed", done => {
      userRepository.findOne = sinon.fake.returns(null);

      usersService.doesUserExist(userId).catch(error => {
        expect(error.context.code).to.equal(
          API_ERROR_CODES.USER_NOT_FOUND.code
        );
        done();
      });
    });
  });

  describe("Remove Group from User by Id", () => {
    const groupId = 1;
    const userId = "<EMAIL>";
    const responseGroup = new UserGroup();

    beforeEach(() => {
      responseGroup.userId = userId;
      responseGroup.groupId = groupId;
      userGroupRepository.delete = sinon.fake.returns(true);
    });

    it("should remove a group from a user", async () => {
      await usersService.removeGroupFromUser(userId, groupId);
      expect(userGroupRepository.delete.calledOnce).equals(true);
    });
  });

  describe("Remove Partner from User by Id", () => {
    const partnerId = "partnerID";
    const userId = "<EMAIL>";
    const responseUserPartnerDirect = new UserPartnerDirect();

    beforeEach(() => {
      responseUserPartnerDirect.userId = userId;
      responseUserPartnerDirect.partnerId = partnerId;
      userPartnerDirectRepository.delete = sinon.fake.returns(true);
    });

    it("should remove a partner from a user", async () => {
      await usersService.removePartnerFromUser(userId, partnerId);
      expect(userPartnerDirectRepository.delete.calledOnce).equals(true);
    });
  });

  describe("Add User", () => {
    it("when add group returns group", async () => {
      const newUser = new User();
      newUser.id = "newUser";
      userRepository.save = sinon.fake.returns(newUser);
      const createdUser = await usersService.addUser(newUser);

      expect(userRepository.save.calledOnce);
      expect(createdUser.id).to.equal(newUser.id);
    });

    it("when add duplicate user throws error", done => {
      const newUser = new User();
      newUser.id = "newUser";
      userRepository.save = sinon.stub().throws({
        code: ORM_ERRORS.DUPLICATE_ENTRY
      });

      usersService.addUser(newUser).catch(error => {
        expect(error).instanceOf(errors.ConflictError);
        done();
      });
    });

    it("when add user with long id throws error", done => {
      const newUser = new User();
      newUser.id =
        "newUsernewUsernewUsernewUsernewUsernewUsernewUsernewUsernewUsernewUsernewUsernewUsernewUsernewUsernewUsernewUsernewUsernewUsernewUsernewUsernewUsernewUsernewUsernewUsernewUsernewUsernewUsernewUsernewUsernewUsernewUsernewUsernewUsernewUsernewUsernewUsernewUsernewUsernewUsernewUsernewUsernewUser";

      usersService.addUser(newUser).catch(error => {
        expect(error).instanceOf(errors.InvalidContentError);
        done();
      });
    });

    it("when add user throws some other error", done => {
      const newUser = new User();
      newUser.id = "newUser";
      userRepository.save = sinon.stub().throws(new Error());

      usersService.addUser(newUser).catch(error => {
        expect(error).instanceOf(errors.InternalError);
        done();
      });
    });
  });

  describe("Get partners for user", () => {
    const partnerId: string = "1";
    const userId: string = "some-id";
    const groupId: number = 1;
    const user: User = {
      id: userId,
      partners: [{ userId, partnerId }]
    };
    const partners: Partner[] = [
      {
        id: partnerId
      }
    ];
    const groups: Group[] = [
      {
        isEditable: false,
        name: "group name",
        coversAllPartners: false,
        id: groupId,
        partnerMappings: [
          {
            partnerId: "another partnerId",
            groupId
          }
        ]
      }
    ];

    beforeEach(() => {
      userRepository.findOne = sinon.fake.returns(user);
      groupsService.getGroupsByUser = sinon.fake.returns(groups);
      partnersService.getPartnersByIds = sinon.fake.returns(partners);
      partnersService.getAllPartners = sinon.fake.returns([]);
    });

    it("should return just the partners of the user when not checking in the groups", done => {
      usersService
        .getPartnersForUser(userId, false)
        .then((response: Partner[]) => {
          expect(groupsService.getGroupsByUser.calledOnce).equals(false);
          expect(partnersService.getPartnersByIds.calledOnce).equals(true);
          expect(partnersService.getAllPartners.calledOnce).equals(false);
          expect(response.length).equals(1);
          done();
        });
    });

    it("should return  all partners of the user when checking in the groups", done => {
      partnersService.getPartnersByIds = sinon.fake.returns([
        { partnerId },
        { partnerId: "another partnerId" }
      ]);
      usersService
        .getPartnersForUser(userId, true)
        .then((response: Partner[]) => {
          expect(groupsService.getGroupsByUser.calledOnce).equals(true);
          expect(partnersService.getPartnersByIds.calledOnce).equals(true);
          expect(partnersService.getAllPartners.calledOnce).equals(false);
          expect(response.length).equals(2);
          done();
        });
    });

    it("Should return all partners if user belongs to a group with 'coversAllPartners'", done => {
      groups[0].coversAllPartners = true;
      groupsService.getGroupsByUser = sinon.fake.returns(groups);
      partnersService.getAllPartners = sinon.fake.returns([
        { partnerId },
        { partnerId },
        { partnerId }
      ]);

      usersService
        .getPartnersForUser(userId, true)
        .then((response: Partner[]) => {
          expect(groupsService.getGroupsByUser.calledOnce).equals(true);
          expect(partnersService.getPartnersByIds.calledOnce).equals(false);
          expect(partnersService.getAllPartners.calledOnce).equals(true);
          expect(response.length).equals(3);
          done();
        });
    });
  });
});
